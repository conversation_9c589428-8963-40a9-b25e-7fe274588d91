const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzcwMzAsImV4cCI6MjA2NjYxMzAzMH0.wU8DuhUgDnIlx6WyqU36sfC6-Wq0dc4Z2Ik1xns3oUk'

const supabase = createClient(supabaseUrl, supabaseKey)

async function setupDatabase() {
  console.log('🚀 Setting up Supabase database tables...')
  
  try {
    // First, let's try to create the tables using individual SQL commands
    console.log('📝 Creating video_viewers table...')
    
    const createViewersTable = `
      CREATE TABLE IF NOT EXISTS video_viewers (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
    
    const { error: viewersError } = await supabase.rpc('exec_sql', { sql: createViewersTable })
    
    if (viewersError) {
      console.log('⚠️ Could not use exec_sql, trying alternative approach...')
      
      // Try using the REST API directly
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseKey}`,
          'apikey': supabaseKey
        },
        body: JSON.stringify({ sql: createViewersTable })
      })
      
      if (!response.ok) {
        console.log('ℹ️ Direct SQL execution not available with anon key')
        console.log('📋 Let me try creating tables through the client...')
        
        // Test if tables already exist by trying to query them
        const { data: testData, error: testError } = await supabase
          .from('video_viewers')
          .select('id')
          .limit(1)
        
        if (testError && testError.code === '42P01') {
          console.log('❌ Tables do not exist and cannot be created with anon key')
          console.log('🔧 You need to run the SQL manually in Supabase dashboard')
          console.log('📋 Go to: https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
          console.log('📄 Copy the contents of database-schema.sql and run it')
          return
        } else if (!testError) {
          console.log('✅ video_viewers table already exists!')
        }
        
        // Test video_analytics table
        const { data: analyticsData, error: analyticsError } = await supabase
          .from('video_analytics')
          .select('id')
          .limit(1)
        
        if (analyticsError && analyticsError.code === '42P01') {
          console.log('❌ video_analytics table does not exist')
          console.log('🔧 Please run the SQL manually in Supabase dashboard')
          return
        } else if (!analyticsError) {
          console.log('✅ video_analytics table already exists!')
        }
        
        console.log('🎉 Database setup complete! Tables are ready.')
        
      } else {
        console.log('✅ video_viewers table created successfully')
      }
    } else {
      console.log('✅ video_viewers table created successfully')
    }

    // Test the connection by inserting and then deleting a test record
    console.log('🔍 Testing database connection...')
    
    const testEmail = `test-${Date.now()}@example.com`
    const { data: insertData, error: insertError } = await supabase
      .from('video_viewers')
      .insert([{ name: 'Test User', email: testEmail }])
      .select()
    
    if (insertError) {
      console.log('❌ Cannot insert test data:', insertError.message)
      console.log('🔧 Please check your database setup')
    } else {
      console.log('✅ Database insert test successful!')
      
      // Clean up test data
      await supabase
        .from('video_viewers')
        .delete()
        .eq('email', testEmail)
      
      console.log('🧹 Test data cleaned up')
    }

    console.log('🎉 Database setup and testing complete!')
    console.log('🚀 Your AI Filmmaking landing page is ready to use!')

  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    console.log('\n📋 Manual Setup Required:')
    console.log('1. Go to: https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
    console.log('2. Copy and paste the contents of database-schema.sql')
    console.log('3. Click "Run" to execute the SQL')
    console.log('4. Restart your development server: npm run dev')
  }
}

setupDatabase()
