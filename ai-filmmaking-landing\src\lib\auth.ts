import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Auth types
export interface User {
  id: string
  email: string
  name: string
  plan: 'free' | 'pro' | 'enterprise'
  subscription_status: string
  created_at: string
}

export interface AuthResponse {
  user: User | null
  error: string | null
}

// Sign up new user
export const signUp = async (email: string, password: string, name: string): Promise<AuthResponse> => {
  try {
    // First create auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    })

    if (authError) {
      return { user: null, error: authError.message }
    }

    if (!authData.user) {
      return { user: null, error: 'Failed to create user' }
    }

    // Then create user profile
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert([{
        id: authData.user.id,
        email,
        name,
        plan: 'free',
        subscription_status: 'active'
      }])
      .select()
      .single()

    if (userError) {
      return { user: null, error: userError.message }
    }

    return { user: userData, error: null }
  } catch (error) {
    return { user: null, error: 'An unexpected error occurred' }
  }
}

// Sign in user
export const signIn = async (email: string, password: string): Promise<AuthResponse> => {
  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (authError) {
      return { user: null, error: authError.message }
    }

    if (!authData.user) {
      return { user: null, error: 'Invalid credentials' }
    }

    // Get user profile
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (userError) {
      return { user: null, error: 'Failed to load user profile' }
    }

    return { user: userData, error: null }
  } catch (error) {
    return { user: null, error: 'An unexpected error occurred' }
  }
}

// Sign out user
export const signOut = async (): Promise<{ error: string | null }> => {
  try {
    const { error } = await supabase.auth.signOut()
    return { error: error?.message || null }
  } catch (error) {
    return { error: 'An unexpected error occurred' }
  }
}

// Get current user
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const { data: { user: authUser } } = await supabase.auth.getUser()
    
    if (!authUser) {
      return null
    }

    const { data: userData, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', authUser.id)
      .single()

    if (error) {
      return null
    }

    return userData
  } catch (error) {
    return null
  }
}

// Check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  const user = await getCurrentUser()
  return user !== null
}

// Password reset
export const resetPassword = async (email: string): Promise<{ error: string | null }> => {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/reset-password`,
    })
    return { error: error?.message || null }
  } catch (error) {
    return { error: 'An unexpected error occurred' }
  }
}

// Update password
export const updatePassword = async (newPassword: string): Promise<{ error: string | null }> => {
  try {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    })
    return { error: error?.message || null }
  } catch (error) {
    return { error: 'An unexpected error occurred' }
  }
}

// Update user profile
export const updateProfile = async (updates: Partial<User>): Promise<AuthResponse> => {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return { user: null, error: 'Not authenticated' }
    }

    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single()

    if (error) {
      return { user: null, error: error.message }
    }

    return { user: data, error: null }
  } catch (error) {
    return { user: null, error: 'An unexpected error occurred' }
  }
}
