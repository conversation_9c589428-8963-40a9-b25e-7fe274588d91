import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface VideoViewer {
  id: string
  name: string
  email: string
  created_at: string
}

export interface VideoAnalytics {
  id: string
  viewer_id: string
  video_id: string
  watch_time: number
  total_duration: number
  completion_percentage: number
  current_position: number
  session_start: string
  session_end?: string
  created_at: string
  updated_at: string
}

// Database functions
export const insertViewer = async (name: string, email: string) => {
  const { data, error } = await supabase
    .from('video_viewers')
    .insert([{ name, email }])
    .select()
    .single()

  if (error) throw error
  return data
}

export const insertVideoAnalytics = async (analytics: Omit<VideoAnalytics, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('video_analytics')
    .insert([analytics])
    .select()
    .single()

  if (error) throw error
  return data
}

export const updateVideoAnalytics = async (id: string, updates: Partial<VideoAnalytics>) => {
  const { data, error } = await supabase
    .from('video_analytics')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data
}
