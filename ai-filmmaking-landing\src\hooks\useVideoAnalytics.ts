import { useState, useEffect, useRef } from 'react'
import { insertVideoAnalytics, updateVideoAnalytics } from '@/lib/supabase'

interface VideoAnalyticsData {
  viewerId: string
  videoId: string
  videoDuration: number
}

export function useVideoAnalytics({ viewerId, videoId, videoDuration }: VideoAnalyticsData) {
  const [analyticsId, setAnalyticsId] = useState<string | null>(null)
  const [totalWatchTime, setTotalWatchTime] = useState(0)
  const [currentPosition, setCurrentPosition] = useState(0)
  const [sessionStart] = useState(new Date().toISOString())
  const [milestones, setMilestones] = useState<Set<number>>(new Set())
  
  const lastUpdateRef = useRef<number>(0)
  const watchTimeRef = useRef<number>(0)

  // Initialize analytics record
  const initializeAnalytics = async () => {
    try {
      const analytics = await insertVideoAnalytics({
        viewer_id: viewerId,
        video_id: videoId,
        watch_time: 0,
        total_duration: videoDuration,
        completion_percentage: 0,
        current_position: 0,
        session_start: sessionStart
      })
      setAnalyticsId(analytics.id)
      return analytics.id
    } catch (error) {
      console.error('Error creating analytics record:', error)
      return null
    }
  }

  // Update watch time
  const updateWatchTime = (position: number) => {
    const now = Date.now()
    const timeDiff = (now - lastUpdateRef.current) / 1000
    
    // Only count as watch time if less than 2 seconds have passed (not seeking)
    if (lastUpdateRef.current > 0 && timeDiff < 2 && timeDiff > 0) {
      watchTimeRef.current += timeDiff
      setTotalWatchTime(watchTimeRef.current)
    }
    
    lastUpdateRef.current = now
    setCurrentPosition(position)
    
    // Track milestones (25%, 50%, 75%, 100%)
    const percentage = (position / videoDuration) * 100
    const newMilestones = new Set(milestones)
    
    if (percentage >= 25 && !milestones.has(25)) {
      newMilestones.add(25)
      console.log('25% milestone reached')
    }
    if (percentage >= 50 && !milestones.has(50)) {
      newMilestones.add(50)
      console.log('50% milestone reached')
    }
    if (percentage >= 75 && !milestones.has(75)) {
      newMilestones.add(75)
      console.log('75% milestone reached')
    }
    if (percentage >= 100 && !milestones.has(100)) {
      newMilestones.add(100)
      console.log('Video completed!')
    }
    
    if (newMilestones.size !== milestones.size) {
      setMilestones(newMilestones)
    }
  }

  // Update analytics in database
  const updateAnalyticsInDB = async (forceUpdate = false) => {
    if (!analyticsId) return

    const completionPercentage = Math.min((watchTimeRef.current / videoDuration) * 100, 100)
    
    try {
      await updateVideoAnalytics(analyticsId, {
        watch_time: Math.floor(watchTimeRef.current),
        completion_percentage: Math.round(completionPercentage * 100) / 100,
        current_position: Math.floor(currentPosition),
        session_end: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error updating analytics:', error)
    }
  }

  // Periodic updates every 10 seconds
  useEffect(() => {
    if (!analyticsId) return

    const interval = setInterval(() => {
      updateAnalyticsInDB()
    }, 10000)

    return () => clearInterval(interval)
  }, [analyticsId, currentPosition])

  // Final update on unmount
  useEffect(() => {
    return () => {
      if (analyticsId) {
        updateAnalyticsInDB(true)
      }
    }
  }, [analyticsId])

  return {
    analyticsId,
    totalWatchTime: Math.floor(totalWatchTime),
    currentPosition,
    completionPercentage: Math.min((totalWatchTime / videoDuration) * 100, 100),
    milestones: Array.from(milestones),
    initializeAnalytics,
    updateWatchTime,
    updateAnalyticsInDB
  }
}
