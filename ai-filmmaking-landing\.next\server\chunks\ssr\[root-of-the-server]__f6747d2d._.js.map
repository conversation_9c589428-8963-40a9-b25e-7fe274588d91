{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'\n\n// Check if we have valid Supabase credentials\nconst hasValidCredentials = supabaseUrl !== 'https://placeholder.supabase.co' &&\n                           supabaseAnonKey !== 'placeholder-key' &&\n                           supabaseUrl.includes('supabase.co')\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface VideoViewer {\n  id: string\n  name: string\n  email: string\n  created_at: string\n}\n\nexport interface VideoAnalytics {\n  id: string\n  viewer_id: string\n  video_id: string\n  watch_time: number\n  total_duration: number\n  completion_percentage: number\n  current_position: number\n  session_start: string\n  session_end?: string\n  created_at: string\n  updated_at: string\n}\n\n// Database functions\nexport const insertViewer = async (name: string, email: string) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Using mock data.')\n    return {\n      id: 'mock-' + Date.now(),\n      name,\n      email,\n      created_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_viewers')\n    .insert([{ name, email }])\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport const insertVideoAnalytics = async (analytics: Omit<VideoAnalytics, 'id' | 'created_at' | 'updated_at'>) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Using mock data.')\n    return {\n      id: 'mock-analytics-' + Date.now(),\n      ...analytics,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_analytics')\n    .insert([analytics])\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport const updateVideoAnalytics = async (id: string, updates: Partial<VideoAnalytics>) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Mock update for analytics:', id, updates)\n    return {\n      id,\n      ...updates,\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_analytics')\n    .update({ ...updates, updated_at: new Date().toISOString() })\n    .eq('id', id)\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,cAAc,uEAAwC;AAC5D,MAAM,kBAAkB,uDAA6C;AAErE,8CAA8C;AAC9C,MAAM,sBAAsB,gBAAgB,qCACjB,oBAAoB,qBACpB,YAAY,QAAQ,CAAC;AAEzC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAyB3C,MAAM,eAAe,OAAO,MAAc;IAC/C,wCAA0B;QACxB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,IAAI,UAAU,KAAK,GAAG;YACtB;YACA;YACA,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;;IAEA,MAAQ,kBAAM;AAQhB;AAEO,MAAM,uBAAuB,OAAO;IACzC,wCAA0B;QACxB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,IAAI,oBAAoB,KAAK,GAAG;YAChC,GAAG,SAAS;YACZ,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;;IAEA,MAAQ,kBAAM;AAQhB;AAEO,MAAM,uBAAuB,OAAO,IAAY;IACrD,wCAA0B;QACxB,QAAQ,IAAI,CAAC,uDAAuD,IAAI;QACxE,OAAO;YACL;YACA,GAAG,OAAO;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;;IAEA,MAAQ,kBAAM;AAShB", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/components/VideoPlayer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { insertVideoAnalytics, updateVideoAnalytics } from '@/lib/supabase'\n\ninterface VideoPlayerProps {\n  viewerId: string\n}\n\ndeclare global {\n  interface Window {\n    YT: any\n    onYouTubeIframeAPIReady: () => void\n  }\n}\n\nexport default function VideoPlayer({ viewerId }: VideoPlayerProps) {\n  const playerRef = useRef<any>(null)\n  const [analyticsId, setAnalyticsId] = useState<string | null>(null)\n  const [sessionStart] = useState(new Date().toISOString())\n  const [lastPosition, setLastPosition] = useState(0)\n  const [totalWatchTime, setTotalWatchTime] = useState(0)\n  const intervalRef = useRef<NodeJS.Timeout | null>(null)\n\n  const videoId = process.env.NEXT_PUBLIC_YOUTUBE_VIDEO_ID || 's-hLAqtNJvg'\n  const videoDuration = 707 // 11:47 in seconds\n\n  useEffect(() => {\n    // Load YouTube IFrame API\n    if (!window.YT) {\n      const tag = document.createElement('script')\n      tag.src = 'https://www.youtube.com/iframe_api'\n      const firstScriptTag = document.getElementsByTagName('script')[0]\n      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)\n    }\n\n    // Initialize player when API is ready\n    const initializePlayer = () => {\n      if (window.YT && window.YT.Player) {\n        playerRef.current = new window.YT.Player('youtube-player', {\n          height: '100%',\n          width: '100%',\n          videoId: videoId,\n          playerVars: {\n            autoplay: 1,\n            controls: 0,\n            disablekb: 1,\n            fs: 0,\n            iv_load_policy: 3,\n            modestbranding: 1,\n            playsinline: 1,\n            rel: 0,\n            showinfo: 0,\n            cc_load_policy: 0,\n            hl: 'en',\n            color: 'white'\n          },\n          events: {\n            onReady: onPlayerReady,\n            onStateChange: onPlayerStateChange\n          }\n        })\n      }\n    }\n\n    if (window.YT && window.YT.Player) {\n      initializePlayer()\n    } else {\n      window.onYouTubeIframeAPIReady = initializePlayer\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current)\n      }\n      if (playerRef.current && playerRef.current.destroy) {\n        playerRef.current.destroy()\n      }\n    }\n  }, [viewerId])\n\n  const onPlayerReady = async () => {\n    // Create initial analytics record\n    try {\n      const analytics = await insertVideoAnalytics({\n        viewer_id: viewerId,\n        video_id: videoId,\n        watch_time: 0,\n        total_duration: videoDuration,\n        completion_percentage: 0,\n        current_position: 0,\n        session_start: sessionStart\n      })\n      setAnalyticsId(analytics.id)\n    } catch (error) {\n      console.error('Error creating analytics record:', error)\n    }\n  }\n\n  const onPlayerStateChange = (event: any) => {\n    const state = event.data\n    \n    if (state === window.YT.PlayerState.PLAYING) {\n      startTracking()\n    } else if (state === window.YT.PlayerState.PAUSED || \n               state === window.YT.PlayerState.ENDED) {\n      stopTracking()\n      updateAnalytics()\n    }\n  }\n\n  const startTracking = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current)\n    }\n\n    intervalRef.current = setInterval(() => {\n      if (playerRef.current && playerRef.current.getCurrentTime) {\n        const currentTime = playerRef.current.getCurrentTime()\n        const timeDiff = Math.abs(currentTime - lastPosition)\n        \n        // Only count as watch time if the difference is reasonable (not seeking)\n        if (timeDiff < 2) {\n          setTotalWatchTime(prev => prev + 1)\n        }\n        \n        setLastPosition(currentTime)\n        \n        // Update analytics every 10 seconds\n        if (totalWatchTime % 10 === 0) {\n          updateAnalytics(currentTime)\n        }\n      }\n    }, 1000)\n  }\n\n  const stopTracking = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current)\n      intervalRef.current = null\n    }\n  }\n\n  const updateAnalytics = async (currentPosition?: number) => {\n    if (!analyticsId) return\n\n    const position = currentPosition || (playerRef.current?.getCurrentTime() || 0)\n    const completionPercentage = (totalWatchTime / videoDuration) * 100\n\n    try {\n      await updateVideoAnalytics(analyticsId, {\n        watch_time: totalWatchTime,\n        completion_percentage: Math.min(completionPercentage, 100),\n        current_position: Math.floor(position),\n        session_end: new Date().toISOString()\n      })\n    } catch (error) {\n      console.error('Error updating analytics:', error)\n    }\n  }\n\n  // Update analytics when component unmounts\n  useEffect(() => {\n    return () => {\n      updateAnalytics()\n    }\n  }, [totalWatchTime, analyticsId])\n\n  return (\n    <div className=\"relative w-full h-full bg-black\">\n      <div id=\"youtube-player\" className=\"w-full h-full\" />\n      \n      {/* Custom overlay to prevent right-click and other interactions */}\n      <div className=\"absolute inset-0 pointer-events-none\" />\n      \n      {/* Progress indicator (optional) */}\n      <div className=\"absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-1\">\n        <span className=\"text-white text-sm\">\n          Watch time: {Math.floor(totalWatchTime / 60)}:{(totalWatchTime % 60).toString().padStart(2, '0')}\n        </span>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBe,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAElD,MAAM,UAAU,mDAA4C;IAC5D,MAAM,gBAAgB,IAAI,mBAAmB;;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,IAAI,CAAC,OAAO,EAAE,EAAE;YACd,MAAM,MAAM,SAAS,aAAa,CAAC;YACnC,IAAI,GAAG,GAAG;YACV,MAAM,iBAAiB,SAAS,oBAAoB,CAAC,SAAS,CAAC,EAAE;YACjE,eAAe,UAAU,EAAE,aAAa,KAAK;QAC/C;QAEA,sCAAsC;QACtC,MAAM,mBAAmB;YACvB,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC,MAAM,EAAE;gBACjC,UAAU,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,kBAAkB;oBACzD,QAAQ;oBACR,OAAO;oBACP,SAAS;oBACT,YAAY;wBACV,UAAU;wBACV,UAAU;wBACV,WAAW;wBACX,IAAI;wBACJ,gBAAgB;wBAChB,gBAAgB;wBAChB,aAAa;wBACb,KAAK;wBACL,UAAU;wBACV,gBAAgB;wBAChB,IAAI;wBACJ,OAAO;oBACT;oBACA,QAAQ;wBACN,SAAS;wBACT,eAAe;oBACjB;gBACF;YACF;QACF;QAEA,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC,MAAM,EAAE;YACjC;QACF,OAAO;YACL,OAAO,uBAAuB,GAAG;QACnC;QAEA,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;YACnC;YACA,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,OAAO,EAAE;gBAClD,UAAU,OAAO,CAAC,OAAO;YAC3B;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB;QACpB,kCAAkC;QAClC,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE;gBAC3C,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,uBAAuB;gBACvB,kBAAkB;gBAClB,eAAe;YACjB;YACA,eAAe,UAAU,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ,MAAM,IAAI;QAExB,IAAI,UAAU,OAAO,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE;YAC3C;QACF,OAAO,IAAI,UAAU,OAAO,EAAE,CAAC,WAAW,CAAC,MAAM,IACtC,UAAU,OAAO,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE;YAChD;YACA;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;QACnC;QAEA,YAAY,OAAO,GAAG,YAAY;YAChC,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,cAAc,EAAE;gBACzD,MAAM,cAAc,UAAU,OAAO,CAAC,cAAc;gBACpD,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc;gBAExC,yEAAyE;gBACzE,IAAI,WAAW,GAAG;oBAChB,kBAAkB,CAAA,OAAQ,OAAO;gBACnC;gBAEA,gBAAgB;gBAEhB,oCAAoC;gBACpC,IAAI,iBAAiB,OAAO,GAAG;oBAC7B,gBAAgB;gBAClB;YACF;QACF,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;YACjC,YAAY,OAAO,GAAG;QACxB;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,aAAa;QAElB,MAAM,WAAW,mBAAoB,UAAU,OAAO,EAAE,oBAAoB;QAC5E,MAAM,uBAAuB,AAAC,iBAAiB,gBAAiB;QAEhE,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa;gBACtC,YAAY;gBACZ,uBAAuB,KAAK,GAAG,CAAC,sBAAsB;gBACtD,kBAAkB,KAAK,KAAK,CAAC;gBAC7B,aAAa,IAAI,OAAO,WAAW;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;QAAgB;KAAY;IAEhC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,IAAG;gBAAiB,WAAU;;;;;;0BAGnC,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;wBAAqB;wBACtB,KAAK,KAAK,CAAC,iBAAiB;wBAAI;wBAAE,CAAC,iBAAiB,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;;AAKtG", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/components/VideoModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { X, Mail, User } from 'lucide-react'\nimport { insertViewer } from '@/lib/supabase'\nimport VideoPlayer from './VideoPlayer'\n\nconst formSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address')\n})\n\ntype FormData = z.infer<typeof formSchema>\n\ninterface VideoModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport default function VideoModal({ isOpen, onClose }: VideoModalProps) {\n  const [showVideo, setShowVideo] = useState(false)\n  const [viewerId, setViewerId] = useState<string | null>(null)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset\n  } = useForm<FormData>({\n    resolver: zodResolver(formSchema)\n  })\n\n  useEffect(() => {\n    if (!isOpen) {\n      setShowVideo(false)\n      setViewerId(null)\n      reset()\n    }\n  }, [isOpen, reset])\n\n  const onSubmit = async (data: FormData) => {\n    setIsSubmitting(true)\n    try {\n      const viewer = await insertViewer(data.name, data.email)\n      setViewerId(viewer.id)\n      setShowVideo(true)\n    } catch (error) {\n      console.error('Error saving viewer:', error)\n      // Handle error - maybe show a toast notification\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div \n        className=\"absolute inset-0 bg-black/80 backdrop-blur-sm\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div className=\"relative bg-slate-900 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n        {/* Close Button */}\n        <button\n          onClick={onClose}\n          className=\"absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 rounded-full p-2 transition-colors\"\n        >\n          <X className=\"w-6 h-6 text-white\" />\n        </button>\n\n        {!showVideo ? (\n          /* Lead Capture Form */\n          <div className=\"p-8 lg:p-12\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Watch the Full Course Preview\n              </h2>\n              <p className=\"text-gray-300 text-lg\">\n                Enter your details to unlock the complete 11:47 minute preview and see what you'll learn\n              </p>\n            </div>\n\n            <form onSubmit={handleSubmit(onSubmit)} className=\"max-w-md mx-auto space-y-6\">\n              <div>\n                <label className=\"block text-white text-sm font-medium mb-2\">\n                  Full Name\n                </label>\n                <div className=\"relative\">\n                  <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    {...register('name')}\n                    type=\"text\"\n                    className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                    placeholder=\"Enter your full name\"\n                  />\n                </div>\n                {errors.name && (\n                  <p className=\"text-red-400 text-sm mt-1\">{errors.name.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-white text-sm font-medium mb-2\">\n                  Email Address\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    {...register('email')}\n                    type=\"email\"\n                    className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                    placeholder=\"Enter your email address\"\n                  />\n                </div>\n                {errors.email && (\n                  <p className=\"text-red-400 text-sm mt-1\">{errors.email.message}</p>\n                )}\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105\"\n              >\n                {isSubmitting ? 'Loading...' : 'Watch Preview Now'}\n              </button>\n            </form>\n\n            <div className=\"text-center mt-6 text-gray-400 text-sm\">\n              <p>🔒 Your information is secure and will never be shared</p>\n            </div>\n          </div>\n        ) : (\n          /* Video Player */\n          <div className=\"aspect-video\">\n            <VideoPlayer viewerId={viewerId!} />\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,MAAM,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AASe,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAmB;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAY;QACpB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;YACX,aAAa;YACb,YAAY;YACZ;QACF;IACF,GAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK;YACvD,YAAY,OAAO,EAAE;YACrB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,iDAAiD;QACnD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;oBAGd,CAAC,YACA,qBAAqB,iBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAK,UAAU,aAAa;gCAAW,WAAU;;kDAChD,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAG7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDACE,GAAG,SAAS,OAAO;wDACpB,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;4CAGf,OAAO,IAAI,kBACV,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kDAIjE,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAG7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDACE,GAAG,SAAS,QAAQ;wDACrB,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;4CAGf,OAAO,KAAK,kBACX,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kDAIlE,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,eAAe,eAAe;;;;;;;;;;;;0CAInC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAE;;;;;;;;;;;;;;;;+BAIP,gBAAgB,iBAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,UAAW;4BAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMnC", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Play, Star, CheckCircle, Users, Clock, Award } from 'lucide-react'\nimport VideoModal from '@/components/VideoModal'\nimport ConfigNotice from '@/components/ConfigNotice'\n\nexport default function Home() {\n  const [isModalOpen, setIsModalOpen] = useState(false)\n\n  const features = [\n    {\n      icon: <Award className=\"w-6 h-6\" />,\n      title: \"Professional AI Tools\",\n      description: \"Master cutting-edge AI filmmaking tools used by industry professionals\"\n    },\n    {\n      icon: <Users className=\"w-6 h-6\" />,\n      title: \"Expert Instruction\",\n      description: \"Learn from award-winning filmmakers and AI technology experts\"\n    },\n    {\n      icon: <Clock className=\"w-6 h-6\" />,\n      title: \"11+ Hours Content\",\n      description: \"Comprehensive course with hands-on projects and real-world examples\"\n    }\n  ]\n\n  const benefits = [\n    \"Create stunning AI-generated visuals and effects\",\n    \"Streamline your post-production workflow\",\n    \"Generate scripts and storyboards with AI\",\n    \"Master AI voice synthesis and audio enhancement\",\n    \"Build a professional portfolio of AI-enhanced films\",\n    \"Access to exclusive AI filmmaking tools and resources\"\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Header */}\n      <header className=\"relative z-10 px-4 py-6\">\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div className=\"text-2xl font-bold text-white\">\n            AI Filmmaking Pro\n          </div>\n          <div className=\"text-white\">\n            <span className=\"text-sm opacity-75\">Premium Course</span>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"relative px-4 py-12 lg:py-20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {/* Left Column - Content */}\n            <div className=\"text-white space-y-8\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"flex text-yellow-400\">\n                    {[...Array(5)].map((_, i) => (\n                      <Star key={i} className=\"w-5 h-5 fill-current\" />\n                    ))}\n                  </div>\n                  <span className=\"text-sm text-gray-300\">4.9/5 (2,847 reviews)</span>\n                </div>\n\n                <h1 className=\"text-4xl lg:text-6xl font-bold leading-tight\">\n                  Master AI Filmmaking in\n                  <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400\">\n                    {\" \"}30 Days\n                  </span>\n                </h1>\n\n                <p className=\"text-xl text-gray-300 leading-relaxed\">\n                  Transform your filmmaking with cutting-edge AI tools. Create Hollywood-quality\n                  content faster than ever before with our comprehensive course.\n                </p>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg p-6 flex-1\">\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold\">$199</div>\n                    <div className=\"text-sm opacity-90\">One-time payment</div>\n                    <div className=\"text-xs mt-1 line-through opacity-60\">$499 regular price</div>\n                  </div>\n                </div>\n                <div className=\"text-center flex-1\">\n                  <div className=\"text-2xl font-semibold text-green-400\">60% OFF</div>\n                  <div className=\"text-sm text-gray-300\">Limited time offer</div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n                {features.map((feature, index) => (\n                  <div key={index} className=\"bg-white/10 backdrop-blur-sm rounded-lg p-4\">\n                    <div className=\"text-purple-400 mb-2\">{feature.icon}</div>\n                    <h3 className=\"font-semibold text-sm mb-1\">{feature.title}</h3>\n                    <p className=\"text-xs text-gray-300\">{feature.description}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Right Column - Video */}\n            <div className=\"relative\">\n              <div className=\"relative bg-black rounded-2xl overflow-hidden shadow-2xl\">\n                <div className=\"aspect-video relative\">\n                  <img\n                    src=\"https://img.youtube.com/vi/s-hLAqtNJvg/maxresdefault.jpg\"\n                    alt=\"AI Filmmaking Course Preview\"\n                    className=\"w-full h-full object-cover\"\n                  />\n                  <div className=\"absolute inset-0 bg-black/30 flex items-center justify-center\">\n                    <button\n                      onClick={() => setIsModalOpen(true)}\n                      className=\"bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-all duration-300 rounded-full p-6 group\"\n                    >\n                      <Play className=\"w-12 h-12 text-white ml-1 group-hover:scale-110 transition-transform\" />\n                    </button>\n                  </div>\n                  <div className=\"absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-1\">\n                    <span className=\"text-white text-sm font-medium\">11:47</span>\n                  </div>\n                </div>\n              </div>\n              <div className=\"absolute -top-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-2 rounded-full text-sm font-bold\">\n                FREE PREVIEW\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* What You'll Learn Section */}\n      <section className=\"px-4 py-16\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              What You'll Master\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              Transform your creative vision into reality with industry-leading AI tools and techniques\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {benefits.map((benefit, index) => (\n              <div key={index} className=\"flex items-start space-x-4 bg-white/5 backdrop-blur-sm rounded-lg p-6\">\n                <CheckCircle className=\"w-6 h-6 text-green-400 flex-shrink-0 mt-1\" />\n                <span className=\"text-white text-lg\">{benefit}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"px-4 py-16\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <div className=\"bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 lg:p-12 border border-white/10\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              Ready to Transform Your Filmmaking?\n            </h2>\n            <p className=\"text-xl text-gray-300 mb-8\">\n              Join thousands of creators who are already using AI to revolutionize their content\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n              <button className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105\">\n                Get Instant Access - $199\n              </button>\n              <div className=\"text-gray-300 text-sm\">\n                <div>✓ 30-day money-back guarantee</div>\n                <div>✓ Lifetime access to updates</div>\n              </div>\n            </div>\n\n            <div className=\"text-center text-gray-400 text-sm\">\n              <p>Secure payment • Instant access • No monthly fees</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Video Modal */}\n      <VideoModal\n        isOpen={isModalOpen}\n        onClose={() => setIsModalOpen(false)}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgC;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM;yDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;gEAAS,WAAU;+DAAb;;;;;;;;;;kEAGf,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAG1C,8OAAC;gDAAG,WAAU;;oDAA+C;kEAE3D,8OAAC;wDAAK,WAAU;;4DACb;4DAAI;;;;;;;;;;;;;0DAIT,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAMvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAI,WAAU;sEAAuC;;;;;;;;;;;;;;;;;0DAG1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAI3C,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEAAwB,QAAQ,IAAI;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAA8B,QAAQ,KAAK;;;;;;kEACzD,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,WAAW;;;;;;;+CAHjD;;;;;;;;;;;;;;;;0CAUhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS,IAAM,eAAe;wDAC9B,WAAU;kEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;kDAIvD,8OAAC;wCAAI,WAAU;kDAA8H;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrJ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;mCAF9B;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAiM;;;;;;kDAGnN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAI;;;;;;0DACL,8OAAC;0DAAI;;;;;;;;;;;;;;;;;;0CAIT,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC,gIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,eAAe;;;;;;;;;;;;AAItC", "debugId": null}}]}