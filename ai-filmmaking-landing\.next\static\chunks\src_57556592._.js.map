{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'\n\n// Check if we have valid Supabase credentials\nconst hasValidCredentials = supabaseUrl !== 'https://placeholder.supabase.co' &&\n                           supabaseAnonKey !== 'placeholder-key' &&\n                           supabaseUrl.includes('supabase.co')\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface VideoViewer {\n  id: string\n  name: string\n  email: string\n  created_at: string\n}\n\nexport interface VideoAnalytics {\n  id: string\n  viewer_id: string\n  video_id: string\n  watch_time: number\n  total_duration: number\n  completion_percentage: number\n  current_position: number\n  session_start: string\n  session_end?: string\n  created_at: string\n  updated_at: string\n}\n\n// Database functions\nexport const insertViewer = async (name: string, email: string) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Using mock data.')\n    return {\n      id: 'mock-' + Date.now(),\n      name,\n      email,\n      created_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_viewers')\n    .insert([{ name, email }])\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport const insertVideoAnalytics = async (analytics: Omit<VideoAnalytics, 'id' | 'created_at' | 'updated_at'>) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Using mock data.')\n    return {\n      id: 'mock-analytics-' + Date.now(),\n      ...analytics,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_analytics')\n    .insert([analytics])\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport const updateVideoAnalytics = async (id: string, updates: Partial<VideoAnalytics>) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Mock update for analytics:', id, updates)\n    return {\n      id,\n      ...updates,\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_analytics')\n    .update({ ...updates, updated_at: new Date().toISOString() })\n    .eq('id', id)\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n"], "names": [], "mappings": ";;;;;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,8CAA8C;AAC9C,MAAM,sBAAsB,gBAAgB,qCACjB,oBAAoB,qBACpB,YAAY,QAAQ,CAAC;AAEzC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAyB3C,MAAM,eAAe,OAAO,MAAc;IAC/C,IAAI,CAAC,qBAAqB;QACxB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,IAAI,UAAU,KAAK,GAAG;YACtB;YACA;YACA,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;QAAC;YAAE;YAAM;QAAM;KAAE,EACxB,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,MAAM,uBAAuB,OAAO;IACzC,IAAI,CAAC,qBAAqB;QACxB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,IAAI,oBAAoB,KAAK,GAAG;YAChC,GAAG,SAAS;YACZ,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;QAAC;KAAU,EAClB,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,MAAM,uBAAuB,OAAO,IAAY;IACrD,IAAI,CAAC,qBAAqB;QACxB,QAAQ,IAAI,CAAC,uDAAuD,IAAI;QACxE,OAAO;YACL;YACA,GAAG,OAAO;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;QAAE,GAAG,OAAO;QAAE,YAAY,IAAI,OAAO,WAAW;IAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/components/AnalyticsDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { supabase } from '@/lib/supabase'\nimport { Users, Play, Clock, TrendingUp } from 'lucide-react'\n\ninterface ViewerAnalytics {\n  id: string\n  name: string\n  email: string\n  created_at: string\n  watch_time: number\n  completion_percentage: number\n  current_position: number\n  session_start: string\n  session_end?: string\n}\n\nexport default function AnalyticsDashboard() {\n  const [analytics, setAnalytics] = useState<ViewerAnalytics[]>([])\n  const [loading, setLoading] = useState(true)\n  const [stats, setStats] = useState({\n    totalViewers: 0,\n    averageWatchTime: 0,\n    averageCompletion: 0,\n    totalWatchTime: 0\n  })\n\n  useEffect(() => {\n    fetchAnalytics()\n  }, [])\n\n  const fetchAnalytics = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('video_analytics')\n        .select(`\n          *,\n          video_viewers (\n            name,\n            email,\n            created_at\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      const formattedData: ViewerAnalytics[] = data.map((item: any) => ({\n        id: item.id,\n        name: item.video_viewers.name,\n        email: item.video_viewers.email,\n        created_at: item.video_viewers.created_at,\n        watch_time: item.watch_time,\n        completion_percentage: item.completion_percentage,\n        current_position: item.current_position,\n        session_start: item.session_start,\n        session_end: item.session_end\n      }))\n\n      setAnalytics(formattedData)\n\n      // Calculate stats\n      const totalViewers = formattedData.length\n      const totalWatchTime = formattedData.reduce((sum, item) => sum + item.watch_time, 0)\n      const averageWatchTime = totalViewers > 0 ? totalWatchTime / totalViewers : 0\n      const averageCompletion = totalViewers > 0 \n        ? formattedData.reduce((sum, item) => sum + item.completion_percentage, 0) / totalViewers \n        : 0\n\n      setStats({\n        totalViewers,\n        averageWatchTime,\n        averageCompletion,\n        totalWatchTime\n      })\n\n    } catch (error) {\n      console.error('Error fetching analytics:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60)\n    const remainingSeconds = seconds % 60\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-slate-900 flex items-center justify-center\">\n        <div className=\"text-white text-xl\">Loading analytics...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-slate-900 p-6\">\n      <div className=\"max-w-7xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">Video Analytics Dashboard</h1>\n        \n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-slate-800 rounded-lg p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-gray-400 text-sm\">Total Viewers</p>\n                <p className=\"text-2xl font-bold text-white\">{stats.totalViewers}</p>\n              </div>\n              <Users className=\"w-8 h-8 text-blue-400\" />\n            </div>\n          </div>\n          \n          <div className=\"bg-slate-800 rounded-lg p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-gray-400 text-sm\">Avg Watch Time</p>\n                <p className=\"text-2xl font-bold text-white\">{formatTime(Math.floor(stats.averageWatchTime))}</p>\n              </div>\n              <Clock className=\"w-8 h-8 text-green-400\" />\n            </div>\n          </div>\n          \n          <div className=\"bg-slate-800 rounded-lg p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-gray-400 text-sm\">Avg Completion</p>\n                <p className=\"text-2xl font-bold text-white\">{stats.averageCompletion.toFixed(1)}%</p>\n              </div>\n              <TrendingUp className=\"w-8 h-8 text-purple-400\" />\n            </div>\n          </div>\n          \n          <div className=\"bg-slate-800 rounded-lg p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-gray-400 text-sm\">Total Watch Time</p>\n                <p className=\"text-2xl font-bold text-white\">{formatTime(stats.totalWatchTime)}</p>\n              </div>\n              <Play className=\"w-8 h-8 text-red-400\" />\n            </div>\n          </div>\n        </div>\n\n        {/* Analytics Table */}\n        <div className=\"bg-slate-800 rounded-lg overflow-hidden\">\n          <div className=\"px-6 py-4 border-b border-slate-700\">\n            <h2 className=\"text-xl font-semibold text-white\">Viewer Details</h2>\n          </div>\n          \n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-slate-700\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\">\n                    Viewer\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\">\n                    Watch Time\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\">\n                    Completion\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\">\n                    Last Position\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\">\n                    Started At\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-slate-700\">\n                {analytics.map((viewer) => (\n                  <tr key={viewer.id} className=\"hover:bg-slate-700/50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-white\">{viewer.name}</div>\n                        <div className=\"text-sm text-gray-400\">{viewer.email}</div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-white\">\n                      {formatTime(viewer.watch_time)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-16 bg-slate-600 rounded-full h-2 mr-2\">\n                          <div \n                            className=\"bg-green-400 h-2 rounded-full\" \n                            style={{ width: `${Math.min(viewer.completion_percentage, 100)}%` }}\n                          />\n                        </div>\n                        <span className=\"text-sm text-white\">{viewer.completion_percentage.toFixed(1)}%</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-white\">\n                      {formatTime(viewer.current_position)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-400\">\n                      {formatDate(viewer.session_start)}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,cAAc;QACd,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;;;;;QAOT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YAEjB,MAAM,gBAAmC,KAAK,GAAG,CAAC,CAAC,OAAc,CAAC;oBAChE,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,aAAa,CAAC,IAAI;oBAC7B,OAAO,KAAK,aAAa,CAAC,KAAK;oBAC/B,YAAY,KAAK,aAAa,CAAC,UAAU;oBACzC,YAAY,KAAK,UAAU;oBAC3B,uBAAuB,KAAK,qBAAqB;oBACjD,kBAAkB,KAAK,gBAAgB;oBACvC,eAAe,KAAK,aAAa;oBACjC,aAAa,KAAK,WAAW;gBAC/B,CAAC;YAED,aAAa;YAEb,kBAAkB;YAClB,MAAM,eAAe,cAAc,MAAM;YACzC,MAAM,iBAAiB,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;YAClF,MAAM,mBAAmB,eAAe,IAAI,iBAAiB,eAAe;YAC5E,MAAM,oBAAoB,eAAe,IACrC,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,qBAAqB,EAAE,KAAK,eAC3E;YAEJ,SAAS;gBACP;gBACA;gBACA;gBACA;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACrE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAqB;;;;;;;;;;;IAG1C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAGnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAiC,MAAM,YAAY;;;;;;;;;;;;kDAElE,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAiC,WAAW,KAAK,KAAK,CAAC,MAAM,gBAAgB;;;;;;;;;;;;kDAE5F,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDAAiC,MAAM,iBAAiB,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEnF,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAI1B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAiC,WAAW,MAAM,cAAc;;;;;;;;;;;;kDAE/E,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMtB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;sCAGnD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,6LAAC;wCAAM,WAAU;kDACd,UAAU,GAAG,CAAC,CAAC,uBACd,6LAAC;gDAAmB,WAAU;;kEAC5B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAkC,OAAO,IAAI;;;;;;8EAC5D,6LAAC;oEAAI,WAAU;8EAAyB,OAAO,KAAK;;;;;;;;;;;;;;;;;kEAGxD,6LAAC;wDAAG,WAAU;kEACX,WAAW,OAAO,UAAU;;;;;;kEAE/B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,qBAAqB,EAAE,KAAK,CAAC,CAAC;wEAAC;;;;;;;;;;;8EAGtE,6LAAC;oEAAK,WAAU;;wEAAsB,OAAO,qBAAqB,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;kEAGlF,6LAAC;wDAAG,WAAU;kEACX,WAAW,OAAO,gBAAgB;;;;;;kEAErC,6LAAC;wDAAG,WAAU;kEACX,WAAW,OAAO,aAAa;;;;;;;+CAzB3B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCpC;GA3MwB;KAAA", "debugId": null}}]}