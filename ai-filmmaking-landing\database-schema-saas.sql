-- SaaS Video Analytics Tool Database Schema
-- Multi-tenant video analytics platform

-- Users table (SaaS customers)
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  plan VARCHAR(50) DEFAULT 'free', -- free, pro, enterprise
  stripe_customer_id VARCHAR(255),
  subscription_status VARCHAR(50) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Videos table (each user can have multiple videos)
CREATE TABLE videos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  video_url TEXT NOT NULL,
  video_id VARCHAR(255) NOT NULL, -- extracted from URL
  platform VARCHAR(50) NOT NULL, -- youtube, vimeo, wistia
  duration INTEGER, -- in seconds
  thumbnail_url TEXT,
  embed_code TEXT, -- generated embed code
  is_active BOOLEAN DEFAULT true,
  custom_settings JSONB DEFAULT '{}', -- player customization
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video viewers (people who watch the videos)
CREATE TABLE video_viewers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video analytics (tracking data)
CREATE TABLE video_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  viewer_id UUID REFERENCES video_viewers(id) ON DELETE CASCADE,
  watch_time INTEGER DEFAULT 0, -- in seconds
  total_duration INTEGER NOT NULL, -- in seconds
  completion_percentage DECIMAL(5,2) DEFAULT 0.00,
  current_position INTEGER DEFAULT 0, -- in seconds
  max_position_reached INTEGER DEFAULT 0, -- in seconds
  segments_watched JSONB DEFAULT '[]', -- array of watched segments
  session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead forms (customizable forms per video)
CREATE TABLE lead_forms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  title VARCHAR(255) DEFAULT 'Watch Video',
  description TEXT DEFAULT 'Enter your details to watch the video',
  fields JSONB DEFAULT '[{"name":"name","label":"Full Name","required":true},{"name":"email","label":"Email Address","required":true}]',
  styling JSONB DEFAULT '{}', -- custom CSS/styling
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Embed analytics (track embed performance)
CREATE TABLE embed_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  domain VARCHAR(255), -- where the embed is hosted
  page_url TEXT,
  views INTEGER DEFAULT 0,
  conversions INTEGER DEFAULT 0, -- form submissions
  date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(video_id, domain, date)
);

-- Create indexes for better performance
CREATE INDEX idx_videos_user_id ON videos(user_id);
CREATE INDEX idx_videos_platform ON videos(platform);
CREATE INDEX idx_video_viewers_video_id ON video_viewers(video_id);
CREATE INDEX idx_video_viewers_email ON video_viewers(email);
CREATE INDEX idx_video_analytics_video_id ON video_analytics(video_id);
CREATE INDEX idx_video_analytics_viewer_id ON video_analytics(viewer_id);
CREATE INDEX idx_lead_forms_video_id ON lead_forms(video_id);
CREATE INDEX idx_embed_analytics_video_id ON embed_analytics(video_id);
CREATE INDEX idx_embed_analytics_date ON embed_analytics(date);

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_viewers ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE lead_forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE embed_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for videos table
CREATE POLICY "Users can view own videos" ON videos
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own videos" ON videos
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own videos" ON videos
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own videos" ON videos
  FOR DELETE USING (auth.uid() = user_id);

-- Public access for embed functionality
CREATE POLICY "Allow public access to active videos" ON videos
  FOR SELECT USING (is_active = true);

-- RLS Policies for video_viewers (public insert, user can view)
CREATE POLICY "Allow public insert on video_viewers" ON video_viewers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view viewers of their videos" ON video_viewers
  FOR SELECT USING (
    video_id IN (SELECT id FROM videos WHERE user_id = auth.uid())
  );

-- RLS Policies for video_analytics (public insert/update, user can view)
CREATE POLICY "Allow public insert on video_analytics" ON video_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public update on video_analytics" ON video_analytics
  FOR UPDATE USING (true);

CREATE POLICY "Users can view analytics of their videos" ON video_analytics
  FOR SELECT USING (
    video_id IN (SELECT id FROM videos WHERE user_id = auth.uid())
  );

-- RLS Policies for lead_forms
CREATE POLICY "Users can manage lead forms for their videos" ON lead_forms
  FOR ALL USING (
    video_id IN (SELECT id FROM videos WHERE user_id = auth.uid())
  );

CREATE POLICY "Allow public access to lead forms" ON lead_forms
  FOR SELECT USING (true);

-- RLS Policies for embed_analytics
CREATE POLICY "Users can view embed analytics for their videos" ON embed_analytics
  FOR SELECT USING (
    video_id IN (SELECT id FROM videos WHERE user_id = auth.uid())
  );

CREATE POLICY "Allow public insert/update on embed_analytics" ON embed_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public update on embed_analytics" ON embed_analytics
  FOR UPDATE USING (true);

-- Create functions for analytics aggregation
CREATE OR REPLACE FUNCTION get_user_video_stats(user_uuid UUID)
RETURNS TABLE (
  total_videos INTEGER,
  total_views INTEGER,
  total_leads INTEGER,
  avg_completion DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(DISTINCT v.id)::INTEGER as total_videos,
    COUNT(DISTINCT vv.id)::INTEGER as total_views,
    COUNT(DISTINCT vv.id)::INTEGER as total_leads,
    COALESCE(AVG(va.completion_percentage), 0)::DECIMAL as avg_completion
  FROM videos v
  LEFT JOIN video_viewers vv ON v.id = vv.video_id
  LEFT JOIN video_analytics va ON v.id = va.video_id
  WHERE v.user_id = user_uuid AND v.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
