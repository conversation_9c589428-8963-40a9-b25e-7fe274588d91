const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNzAzMCwiZXhwIjoyMDY2NjEzMDMwfQ.I7iuGrqM94GqY5Ya54rlJ1Bo_V2B3LxyM-7yAlN5dcQ'

const supabase = createClient(supabaseUrl, serviceRoleKey)

async function runMigration() {
  console.log('🚀 Starting SaaS Database Migration...')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  
  try {
    // Read the simplified migration SQL file
    const migrationSQL = fs.readFileSync('./migrate-to-saas-simple.sql', 'utf8')
    
    console.log('📄 Migration SQL loaded successfully')
    console.log('⚠️  This migration will:')
    console.log('   • Preserve existing video_viewers and video_analytics data')
    console.log('   • Create new SaaS tables (users, videos, lead_forms, embed_analytics)')
    console.log('   • Set up Row Level Security (RLS) policies')
    console.log('   • Create a demo user if existing data is found')
    console.log('')
    
    // Since we can't execute the full SQL directly, we'll provide instructions
    console.log('📋 MANUAL MIGRATION REQUIRED:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('1. 🌐 Open your Supabase Dashboard SQL Editor:')
    console.log('   https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
    console.log('')
    console.log('2. 📄 Copy and paste the COMPLETE contents of:')
    console.log('   migrate-to-saas-simple.sql')
    console.log('')
    console.log('3. ▶️  Click "Run" to execute the migration')
    console.log('')
    console.log('4. ✅ You should see: "SaaS Database Migration Completed Successfully! 🎉"')
    console.log('')
    console.log('5. 🔄 Run verification: node run-migration.js --verify')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    
    console.log('\n📊 Current Database State:')
    await checkCurrentState()
    
  } catch (error) {
    console.error('❌ Migration preparation failed:', error.message)
  }
}

async function verifyMigration() {
  console.log('🔍 Verifying SaaS Database Migration...')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  
  try {
    const requiredTables = [
      'users',
      'videos', 
      'video_viewers',
      'video_analytics',
      'lead_forms',
      'embed_analytics'
    ]
    
    let allTablesExist = true
    const tableStatus = []
    
    for (const table of requiredTables) {
      try {
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true })
        
        if (error) {
          tableStatus.push({ table, status: 'missing', error: error.message })
          allTablesExist = false
        } else {
          tableStatus.push({ table, status: 'exists', count: count || 0 })
        }
      } catch (err) {
        tableStatus.push({ table, status: 'error', error: err.message })
        allTablesExist = false
      }
    }
    
    console.log('\n📋 Table Status:')
    for (const status of tableStatus) {
      if (status.status === 'exists') {
        console.log(`✅ ${status.table.padEnd(20)} - Ready (${status.count} records)`)
      } else {
        console.log(`❌ ${status.table.padEnd(20)} - ${status.status}`)
      }
    }
    
    if (allTablesExist) {
      console.log('\n🎉 Migration Verification Successful!')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      
      // Test authentication system
      console.log('\n🔐 Testing Authentication System...')
      try {
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email: `test-${Date.now()}@example.com`,
          password: 'testpassword123'
        })
        
        if (!authError) {
          console.log('✅ Authentication system working')
          
          // Clean up test user
          if (authData.user) {
            await supabase.auth.admin.deleteUser(authData.user.id)
          }
        } else {
          console.log('⚠️  Authentication test failed:', authError.message)
        }
      } catch (authTestError) {
        console.log('⚠️  Authentication test error:', authTestError.message)
      }
      
      console.log('\n🚀 SaaS Platform Status: READY!')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      console.log('🎯 Test your platform:')
      console.log('• Landing Page: http://localhost:3000')
      console.log('• Sign Up: http://localhost:3000/signup')
      console.log('• Login: http://localhost:3000/login')
      console.log('• Dashboard: http://localhost:3000/dashboard')
      console.log('')
      console.log('📝 Next Steps:')
      console.log('1. Create a user account')
      console.log('2. Add a video URL (YouTube/Vimeo/Wistia)')
      console.log('3. Generate embed code')
      console.log('4. Test on any website')
      console.log('5. View analytics dashboard')
      
    } else {
      console.log('\n❌ Migration Incomplete')
      console.log('Please run the migration SQL in Supabase Dashboard first.')
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message)
  }
}

async function checkCurrentState() {
  try {
    const { count: viewerCount } = await supabase
      .from('video_viewers')
      .select('*', { count: 'exact', head: true })
    
    const { count: analyticsCount } = await supabase
      .from('video_analytics')
      .select('*', { count: 'exact', head: true })
    
    const { count: userCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
    
    console.log(`• Existing video viewers: ${viewerCount || 0}`)
    console.log(`• Existing analytics records: ${analyticsCount || 0}`)
    console.log(`• Current users: ${userCount || 0}`)
    
    if ((viewerCount > 0 || analyticsCount > 0) && userCount === 0) {
      console.log('\n💡 Migration will create a demo user and preserve your existing data')
    }
    
  } catch (error) {
    console.log('• Could not check current state:', error.message)
  }
}

// Check command line arguments
const args = process.argv.slice(2)
if (args.includes('--verify')) {
  verifyMigration()
} else {
  runMigration()
}
