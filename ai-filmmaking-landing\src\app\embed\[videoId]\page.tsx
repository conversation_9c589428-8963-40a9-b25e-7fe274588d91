'use client'

import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Mail, User, Play } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import UniversalVideoPlayer from '@/components/UniversalVideoPlayer'

const formSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address')
})

type FormData = z.infer<typeof formSchema>

interface Video {
  id: string
  title: string
  platform: string
  video_id: string
  duration?: number
  thumbnail_url?: string
  custom_settings: any
}

interface LeadForm {
  title: string
  description: string
  fields: Array<{
    name: string
    label: string
    required: boolean
  }>
  styling: any
}

export default function EmbedPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const videoId = params.videoId as string
  
  const [video, setVideo] = useState<Video | null>(null)
  const [leadForm, setLeadForm] = useState<LeadForm | null>(null)
  const [showVideo, setShowVideo] = useState(false)
  const [viewerId, setViewerId] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(true)

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(formSchema)
  })

  useEffect(() => {
    loadVideoData()
    trackEmbedView()
  }, [videoId])

  const loadVideoData = async () => {
    try {
      // Load video data
      const { data: videoData, error: videoError } = await supabase
        .from('videos')
        .select('*')
        .eq('id', videoId)
        .eq('is_active', true)
        .single()

      if (videoError || !videoData) {
        setError('Video not found or inactive')
        setLoading(false)
        return
      }

      setVideo(videoData)

      // Load lead form data
      const { data: formData, error: formError } = await supabase
        .from('lead_forms')
        .select('*')
        .eq('video_id', videoId)
        .single()

      if (!formError && formData) {
        setLeadForm(formData)
      }

      setLoading(false)
    } catch (err) {
      setError('Failed to load video')
      setLoading(false)
    }
  }

  const trackEmbedView = async () => {
    try {
      const domain = window.location.hostname
      const pageUrl = document.referrer || window.location.href
      
      // Track embed view
      await supabase
        .from('embed_analytics')
        .upsert({
          video_id: videoId,
          domain,
          page_url: pageUrl,
          views: 1
        }, {
          onConflict: 'video_id,domain,date',
          ignoreDuplicates: false
        })
    } catch (error) {
      console.error('Error tracking embed view:', error)
    }
  }

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    setError('')

    try {
      // Create viewer record
      const { data: viewer, error: viewerError } = await supabase
        .from('video_viewers')
        .insert([{
          video_id: videoId,
          name: data.name,
          email: data.email,
          ip_address: null, // Could be populated server-side
          user_agent: navigator.userAgent,
          referrer: document.referrer
        }])
        .select()
        .single()

      if (viewerError) {
        setError('Failed to save your information')
        return
      }

      setViewerId(viewer.id)
      setShowVideo(true)

      // Track conversion
      const domain = window.location.hostname
      await supabase
        .from('embed_analytics')
        .upsert({
          video_id: videoId,
          domain,
          conversions: 1
        }, {
          onConflict: 'video_id,domain,date',
          ignoreDuplicates: false
        })

    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin" />
      </div>
    )
  }

  if (error || !video) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-lg mb-2">⚠️</div>
          <p className="text-white">{error || 'Video not found'}</p>
        </div>
      </div>
    )
  }

  if (!showVideo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          {/* Video Thumbnail */}
          <div className="relative mb-6">
            <div className="aspect-video bg-black rounded-lg overflow-hidden">
              {video.thumbnail_url ? (
                <img 
                  src={video.thumbnail_url}
                  alt={video.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-800">
                  <Play className="w-16 h-16 text-gray-400" />
                </div>
              )}
              <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                <div className="bg-white/20 backdrop-blur-sm rounded-full p-4">
                  <Play className="w-8 h-8 text-white ml-1" />
                </div>
              </div>
            </div>
          </div>

          {/* Lead Capture Form */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">
                {leadForm?.title || 'Watch Video'}
              </h2>
              <p className="text-gray-300">
                {leadForm?.description || 'Enter your details to watch the video'}
              </p>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {error && (
                <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                  <p className="text-red-200 text-sm">{error}</p>
                </div>
              )}

              {/* Name Field */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Full Name
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    {...register('name')}
                    type="text"
                    className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                </div>
                {errors.name && (
                  <p className="text-red-400 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              {/* Email Field */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    {...register('email')}
                    type="email"
                    className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter your email address"
                  />
                </div>
                {errors.email && (
                  <p className="text-red-400 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                {isSubmitting ? 'Loading...' : 'Watch Video Now'}
              </button>
            </form>

            <div className="text-center mt-4 text-gray-400 text-xs">
              🔒 Your information is secure and will never be shared
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="aspect-video">
        <UniversalVideoPlayer
          videoId={video.video_id}
          platform={video.platform as 'youtube' | 'vimeo' | 'wistia'}
          viewerId={viewerId!}
          duration={video.duration}
        />
      </div>
    </div>
  )
}
