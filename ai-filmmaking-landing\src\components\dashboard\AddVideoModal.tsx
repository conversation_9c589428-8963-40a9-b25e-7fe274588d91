'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Plus, Youtube, Video, Play } from 'lucide-react'
import { createVideo } from '@/lib/videos'
import { isValidVideoUrl, getSupportedPlatforms } from '@/lib/video-platforms'

const videoSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  video_url: z.string().url('Please enter a valid URL').refine(
    (url) => isValidVideoUrl(url),
    'Please enter a valid YouTube, Vimeo, or Wistia URL'
  )
})

type VideoFormData = z.infer<typeof videoSchema>

interface AddVideoModalProps {
  isOpen: boolean
  onClose: () => void
  onVideoAdded: () => void
  userId: string
}

export default function AddVideoModal({ isOpen, onClose, onVideoAdded, userId }: AddVideoModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<VideoFormData>({
    resolver: zodResolver(videoSchema)
  })

  const watchedUrl = watch('video_url')
  const supportedPlatforms = getSupportedPlatforms()

  const onSubmit = async (data: VideoFormData) => {
    setIsSubmitting(true)
    setError('')

    try {
      const { video, error } = await createVideo(userId, data)
      
      if (error) {
        setError(error)
      } else if (video) {
        reset()
        onVideoAdded()
        onClose()
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    reset()
    setError('')
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="relative bg-slate-800 rounded-lg p-6 w-full max-w-md mx-4 border border-slate-700">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">Add New Video</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Error Message */}
          {error && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          )}

          {/* Video URL Field */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Video URL
            </label>
            <div className="relative">
              <Video className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                {...register('video_url')}
                type="url"
                className="w-full pl-10 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="https://www.youtube.com/watch?v=..."
              />
            </div>
            {errors.video_url && (
              <p className="text-red-400 text-sm mt-1">{errors.video_url.message}</p>
            )}
            
            {/* Supported Platforms */}
            <div className="mt-2">
              <p className="text-gray-400 text-xs mb-2">Supported platforms:</p>
              <div className="flex space-x-2">
                {supportedPlatforms.map((platform) => (
                  <span
                    key={platform.key}
                    className="bg-slate-700 text-gray-300 text-xs px-2 py-1 rounded"
                  >
                    {platform.name}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Title Field */}
          <div>
            <label className="block text-white text-sm font-medium mb-2">
              Video Title
            </label>
            <input
              {...register('title')}
              type="text"
              className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Enter a title for your video"
            />
            {errors.title && (
              <p className="text-red-400 text-sm mt-1">{errors.title.message}</p>
            )}
            <p className="text-gray-400 text-xs mt-1">
              Leave empty to auto-detect from video metadata
            </p>
          </div>

          {/* URL Preview */}
          {watchedUrl && isValidVideoUrl(watchedUrl) && (
            <div className="bg-slate-700 rounded-lg p-3">
              <div className="flex items-center space-x-2 text-green-400 text-sm">
                <Play className="w-4 h-4" />
                <span>Valid video URL detected</span>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2"
            >
              {isSubmitting ? (
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <>
                  <Plus className="w-5 h-5" />
                  <span>Add Video</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
