const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNzAzMCwiZXhwIjoyMDY2NjEzMDMwfQ.I7iuGrqM94GqY5Ya54rlJ1Bo_V2B3LxyM-7yAlN5dcQ'

const supabase = createClient(supabaseUrl, serviceRoleKey)

async function testVideoTracking() {
  console.log('🧪 Testing Video Tracking Calculations...')
  
  try {
    // Create test scenarios
    const testScenarios = [
      {
        name: 'Quick Preview (30 seconds)',
        watchTime: 30,
        maxPosition: 30,
        expectedCompletion: (30 / 707) * 100 // ~4.2%
      },
      {
        name: 'Half Video Watched',
        watchTime: 300,
        maxPosition: 354, // 5:54 (half of 11:47)
        expectedCompletion: (354 / 707) * 100 // ~50%
      },
      {
        name: 'Full Video Watched',
        watchTime: 650,
        maxPosition: 707,
        expectedCompletion: 100
      },
      {
        name: 'Skipped Around (watched 2 minutes but reached end)',
        watchTime: 120,
        maxPosition: 707,
        expectedCompletion: 100
      }
    ]
    
    for (let i = 0; i < testScenarios.length; i++) {
      const scenario = testScenarios[i]
      console.log(`\n📊 Testing: ${scenario.name}`)
      
      // Create test viewer
      const testEmail = `test-${Date.now()}-${i}@example.com`
      const { data: viewer, error: viewerError } = await supabase
        .from('video_viewers')
        .insert([{ name: `Test User ${i + 1}`, email: testEmail }])
        .select()
        .single()
      
      if (viewerError) {
        console.log('❌ Failed to create test viewer:', viewerError.message)
        continue
      }
      
      // Calculate completion percentage using the new logic
      const progressCompletion = (scenario.maxPosition / 707) * 100
      const segmentsWatched = Math.min(Math.ceil(scenario.maxPosition / 10), Math.ceil(707 / 10))
      const totalSegments = Math.ceil(707 / 10)
      const engagementCompletion = (segmentsWatched / totalSegments) * 100
      const finalCompletion = Math.max(progressCompletion, engagementCompletion)
      
      // Insert analytics
      const { data: analytics, error: analyticsError } = await supabase
        .from('video_analytics')
        .insert([{
          viewer_id: viewer.id,
          video_id: 's-hLAqtNJvg',
          watch_time: scenario.watchTime,
          total_duration: 707,
          completion_percentage: Math.min(finalCompletion, 100),
          current_position: scenario.maxPosition
        }])
        .select()
        .single()
      
      if (analyticsError) {
        console.log('❌ Failed to insert analytics:', analyticsError.message)
        continue
      }
      
      console.log(`   ✅ Watch Time: ${scenario.watchTime}s (${Math.floor(scenario.watchTime / 60)}:${(scenario.watchTime % 60).toString().padStart(2, '0')})`)
      console.log(`   ✅ Max Position: ${scenario.maxPosition}s (${Math.floor(scenario.maxPosition / 60)}:${(scenario.maxPosition % 60).toString().padStart(2, '0')})`)
      console.log(`   ✅ Completion: ${finalCompletion.toFixed(1)}%`)
      console.log(`   ✅ Progress-based: ${progressCompletion.toFixed(1)}%`)
      console.log(`   ✅ Engagement-based: ${engagementCompletion.toFixed(1)}%`)
      
      // Clean up test data
      await supabase.from('video_analytics').delete().eq('id', analytics.id)
      await supabase.from('video_viewers').delete().eq('id', viewer.id)
    }
    
    console.log('\n🎉 All tracking tests completed!')
    console.log('\n📋 Key Improvements Made:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('✅ Fixed watch time calculation (now tracks actual viewing time)')
    console.log('✅ Improved completion percentage (based on video progress, not just time)')
    console.log('✅ Added segment tracking (prevents gaming the system)')
    console.log('✅ Enhanced progress bars (color-coded by completion level)')
    console.log('✅ Better analytics display (shows both time and progress)')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('\n🧪 Test the improvements:')
    console.log('1. 🎬 Go to: http://localhost:3000')
    console.log('2. 📝 Fill out the lead form')
    console.log('3. ▶️  Watch some of the video (try skipping around)')
    console.log('4. 📊 Check analytics at: http://localhost:3000/admin')
    console.log('5. 🔍 Verify completion bars fill correctly')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testVideoTracking()
