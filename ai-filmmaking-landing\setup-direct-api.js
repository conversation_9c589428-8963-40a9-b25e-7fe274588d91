const https = require('https')
const { URL } = require('url')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNzAzMCwiZXhwIjoyMDY2NjEzMDMwfQ.I7iuGrqM94GqY5Ya54rlJ1Bo_V2B3LxyM-7yAlN5dcQ'

async function executeSQL(sql) {
  return new Promise((resolve, reject) => {
    const url = new URL('/rest/v1/rpc/query', supabaseUrl)
    
    const postData = JSON.stringify({ query: sql })
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceRoleKey}`,
        'apikey': serviceRoleKey,
        'Content-Length': Buffer.byteLength(postData)
      }
    }
    
    const req = https.request(options, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve({ success: true, data: data })
        } else {
          resolve({ success: false, error: `HTTP ${res.statusCode}: ${data}` })
        }
      })
    })
    
    req.on('error', (error) => {
      resolve({ success: false, error: error.message })
    })
    
    req.write(postData)
    req.end()
  })
}

async function createTablesManually() {
  console.log('🚀 Creating database tables using direct PostgreSQL connection...')
  
  // Since we can't execute SQL directly, let's try creating tables using the Supabase client
  const { createClient } = require('@supabase/supabase-js')
  const supabase = createClient(supabaseUrl, serviceRoleKey)
  
  try {
    console.log('📝 Attempting to create tables using schema operations...')
    
    // Let's try to check if tables exist first
    console.log('🔍 Checking existing tables...')
    
    // Try to query video_viewers table
    const { data: viewersCheck, error: viewersError } = await supabase
      .from('video_viewers')
      .select('id')
      .limit(1)
    
    if (viewersError && viewersError.code === '42P01') {
      console.log('❌ video_viewers table does not exist')
      console.log('📋 Manual setup required - tables need to be created in Supabase Dashboard')
      
      console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      console.log('🛠️  MANUAL SETUP INSTRUCTIONS:')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      console.log('1. 🌐 Open your Supabase Dashboard SQL Editor:')
      console.log('   https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
      console.log('')
      console.log('2. 📄 Copy and paste this complete SQL script:')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      
      const fullSQL = `
-- AI Filmmaking Course Database Setup
-- Run this entire script in your Supabase SQL Editor

-- Create video_viewers table
CREATE TABLE video_viewers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create video_analytics table
CREATE TABLE video_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  viewer_id UUID REFERENCES video_viewers(id) ON DELETE CASCADE,
  video_id VARCHAR(255) NOT NULL,
  watch_time INTEGER DEFAULT 0, -- in seconds
  total_duration INTEGER NOT NULL, -- in seconds
  completion_percentage DECIMAL(5,2) DEFAULT 0.00,
  current_position INTEGER DEFAULT 0, -- in seconds
  session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_video_analytics_viewer_id ON video_analytics(viewer_id);
CREATE INDEX idx_video_analytics_video_id ON video_analytics(video_id);
CREATE INDEX idx_video_viewers_email ON video_viewers(email);

-- Enable Row Level Security (RLS)
ALTER TABLE video_viewers ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for public access
CREATE POLICY "Allow public insert on video_viewers" ON video_viewers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on video_viewers" ON video_viewers
  FOR SELECT USING (true);

CREATE POLICY "Allow public insert on video_analytics" ON video_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public update on video_analytics" ON video_analytics
  FOR UPDATE USING (true);

CREATE POLICY "Allow public select on video_analytics" ON video_analytics
  FOR SELECT USING (true);

-- Insert a test record to verify setup
INSERT INTO video_viewers (name, email) VALUES ('Setup Test', '<EMAIL>');

-- Clean up test record
DELETE FROM video_viewers WHERE email = '<EMAIL>';

-- Success message
SELECT 'Database setup completed successfully! 🎉' as message;
`
      
      console.log(fullSQL)
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      console.log('3. ▶️  Click "Run" to execute the entire script')
      console.log('4. ✅ You should see "Database setup completed successfully! 🎉"')
      console.log('5. 🔄 Refresh your landing page at http://localhost:3000')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      
      return false
    } else if (!viewersError) {
      console.log('✅ video_viewers table already exists!')
      
      // Check video_analytics table
      const { data: analyticsCheck, error: analyticsError } = await supabase
        .from('video_analytics')
        .select('id')
        .limit(1)
      
      if (!analyticsError) {
        console.log('✅ video_analytics table already exists!')
        console.log('🎉 Database is already set up and ready!')
        return true
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error.message)
    return false
  }
  
  return false
}

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection and functionality...')
  
  const { createClient } = require('@supabase/supabase-js')
  const supabase = createClient(supabaseUrl, serviceRoleKey)
  
  try {
    // Test inserting a viewer
    const testEmail = `test-${Date.now()}@example.com`
    const { data: viewer, error: viewerError } = await supabase
      .from('video_viewers')
      .insert([{ name: 'Test User', email: testEmail }])
      .select()
      .single()
    
    if (viewerError) {
      console.log('❌ Failed to insert test viewer:', viewerError.message)
      return false
    }
    
    console.log('✅ Successfully inserted test viewer')
    
    // Test inserting analytics
    const { data: analytics, error: analyticsError } = await supabase
      .from('video_analytics')
      .insert([{
        viewer_id: viewer.id,
        video_id: 's-hLAqtNJvg',
        watch_time: 60,
        total_duration: 707,
        completion_percentage: 8.5,
        current_position: 60
      }])
      .select()
      .single()
    
    if (analyticsError) {
      console.log('❌ Failed to insert test analytics:', analyticsError.message)
      return false
    }
    
    console.log('✅ Successfully inserted test analytics')
    
    // Clean up test data
    await supabase.from('video_analytics').delete().eq('id', analytics.id)
    await supabase.from('video_viewers').delete().eq('id', viewer.id)
    console.log('🧹 Test data cleaned up')
    
    console.log('\n🎉 Database is fully functional!')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('✅ Video tracking system ready')
    console.log('✅ Lead capture system ready')
    console.log('✅ Analytics dashboard ready')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('🚀 Test your landing page: http://localhost:3000')
    console.log('📊 View analytics dashboard: http://localhost:3000/admin')
    
    return true
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message)
    return false
  }
}

async function main() {
  const tablesExist = await createTablesManually()
  
  if (tablesExist) {
    await testDatabaseConnection()
  } else {
    console.log('\n⏳ After running the SQL in Supabase Dashboard, run this command again to test:')
    console.log('   node setup-direct-api.js')
  }
}

main()
