{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Auth types\nexport interface User {\n  id: string\n  email: string\n  name: string\n  plan: 'free' | 'pro' | 'enterprise'\n  subscription_status: string\n  created_at: string\n}\n\nexport interface AuthResponse {\n  user: User | null\n  error: string | null\n}\n\n// Sign up new user\nexport const signUp = async (email: string, password: string, name: string): Promise<AuthResponse> => {\n  try {\n    // First create auth user\n    const { data: authData, error: authError } = await supabase.auth.signUp({\n      email,\n      password,\n    })\n\n    if (authError) {\n      return { user: null, error: authError.message }\n    }\n\n    if (!authData.user) {\n      return { user: null, error: 'Failed to create user' }\n    }\n\n    // Then create user profile\n    const { data: userData, error: userError } = await supabase\n      .from('users')\n      .insert([{\n        id: authData.user.id,\n        email,\n        name,\n        plan: 'free',\n        subscription_status: 'active'\n      }])\n      .select()\n      .single()\n\n    if (userError) {\n      return { user: null, error: userError.message }\n    }\n\n    return { user: userData, error: null }\n  } catch (error) {\n    return { user: null, error: 'An unexpected error occurred' }\n  }\n}\n\n// Sign in user\nexport const signIn = async (email: string, password: string): Promise<AuthResponse> => {\n  try {\n    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n\n    if (authError) {\n      return { user: null, error: authError.message }\n    }\n\n    if (!authData.user) {\n      return { user: null, error: 'Invalid credentials' }\n    }\n\n    // Get user profile\n    const { data: userData, error: userError } = await supabase\n      .from('users')\n      .select('*')\n      .eq('id', authData.user.id)\n      .single()\n\n    if (userError) {\n      return { user: null, error: 'Failed to load user profile' }\n    }\n\n    return { user: userData, error: null }\n  } catch (error) {\n    return { user: null, error: 'An unexpected error occurred' }\n  }\n}\n\n// Sign out user\nexport const signOut = async (): Promise<{ error: string | null }> => {\n  try {\n    const { error } = await supabase.auth.signOut()\n    return { error: error?.message || null }\n  } catch (error) {\n    return { error: 'An unexpected error occurred' }\n  }\n}\n\n// Get current user\nexport const getCurrentUser = async (): Promise<User | null> => {\n  try {\n    const { data: { user: authUser } } = await supabase.auth.getUser()\n    \n    if (!authUser) {\n      return null\n    }\n\n    const { data: userData, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('id', authUser.id)\n      .single()\n\n    if (error) {\n      return null\n    }\n\n    return userData\n  } catch (error) {\n    return null\n  }\n}\n\n// Check if user is authenticated\nexport const isAuthenticated = async (): Promise<boolean> => {\n  const user = await getCurrentUser()\n  return user !== null\n}\n\n// Password reset\nexport const resetPassword = async (email: string): Promise<{ error: string | null }> => {\n  try {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/reset-password`,\n    })\n    return { error: error?.message || null }\n  } catch (error) {\n    return { error: 'An unexpected error occurred' }\n  }\n}\n\n// Update password\nexport const updatePassword = async (newPassword: string): Promise<{ error: string | null }> => {\n  try {\n    const { error } = await supabase.auth.updateUser({\n      password: newPassword\n    })\n    return { error: error?.message || null }\n  } catch (error) {\n    return { error: 'An unexpected error occurred' }\n  }\n}\n\n// Update user profile\nexport const updateProfile = async (updates: Partial<User>): Promise<AuthResponse> => {\n  try {\n    const user = await getCurrentUser()\n    if (!user) {\n      return { user: null, error: 'Not authenticated' }\n    }\n\n    const { data, error } = await supabase\n      .from('users')\n      .update(updates)\n      .eq('id', user.id)\n      .select()\n      .single()\n\n    if (error) {\n      return { user: null, error: error.message }\n    }\n\n    return { user: data, error: null }\n  } catch (error) {\n    return { user: null, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAkB3C,MAAM,SAAS,OAAO,OAAe,UAAkB;IAC5D,IAAI;QACF,yBAAyB;QACzB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACtE;YACA;QACF;QAEA,IAAI,WAAW;YACb,OAAO;gBAAE,MAAM;gBAAM,OAAO,UAAU,OAAO;YAAC;QAChD;QAEA,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAwB;QACtD;QAEA,2BAA2B;QAC3B,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,SACL,MAAM,CAAC;YAAC;gBACP,IAAI,SAAS,IAAI,CAAC,EAAE;gBACpB;gBACA;gBACA,MAAM;gBACN,qBAAqB;YACvB;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,WAAW;YACb,OAAO;gBAAE,MAAM;gBAAM,OAAO,UAAU,OAAO;YAAC;QAChD;QAEA,OAAO;YAAE,MAAM;YAAU,OAAO;QAAK;IACvC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM,OAAO;QAA+B;IAC7D;AACF;AAGO,MAAM,SAAS,OAAO,OAAe;IAC1C,IAAI;QACF,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAClF;YACA;QACF;QAEA,IAAI,WAAW;YACb,OAAO;gBAAE,MAAM;gBAAM,OAAO,UAAU,OAAO;YAAC;QAChD;QAEA,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAsB;QACpD;QAEA,mBAAmB;QACnB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,SAAS,IAAI,CAAC,EAAE,EACzB,MAAM;QAET,IAAI,WAAW;YACb,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAA8B;QAC5D;QAEA,OAAO;YAAE,MAAM;YAAU,OAAO;QAAK;IACvC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM,OAAO;QAA+B;IAC7D;AACF;AAGO,MAAM,UAAU;IACrB,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,OAAO;YAAE,OAAO,OAAO,WAAW;QAAK;IACzC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,MAAM,QAAQ,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEhE,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,MAAM;QAET,IAAI,OAAO;YACT,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,OAAO,MAAM;IACnB,OAAO,SAAS;AAClB;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACjE,YAAY,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,oBAAoB,CAAC;QACvE;QACA,OAAO;YAAE,OAAO,OAAO,WAAW;QAAK;IACzC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAC/C,UAAU;QACZ;QACA,OAAO;YAAE,OAAO,OAAO,WAAW;QAAK;IACzC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,OAAO,MAAM;QACnB,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QAEA,OAAO;YAAE,MAAM;YAAM,OAAO;QAAK;IACnC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM,OAAO;QAA+B;IAC7D;AACF", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'\n\n// Check if we have valid Supabase credentials\nconst hasValidCredentials = supabaseUrl !== 'https://placeholder.supabase.co' &&\n                           supabaseAnonKey !== 'placeholder-key' &&\n                           supabaseUrl.includes('supabase.co')\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface VideoViewer {\n  id: string\n  name: string\n  email: string\n  created_at: string\n}\n\nexport interface VideoAnalytics {\n  id: string\n  viewer_id: string\n  video_id: string\n  watch_time: number\n  total_duration: number\n  completion_percentage: number\n  current_position: number\n  session_start: string\n  session_end?: string\n  created_at: string\n  updated_at: string\n}\n\n// Database functions\nexport const insertViewer = async (name: string, email: string) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Using mock data.')\n    return {\n      id: 'mock-' + Date.now(),\n      name,\n      email,\n      created_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_viewers')\n    .insert([{ name, email }])\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport const insertVideoAnalytics = async (analytics: Omit<VideoAnalytics, 'id' | 'created_at' | 'updated_at'>) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Using mock data.')\n    return {\n      id: 'mock-analytics-' + Date.now(),\n      ...analytics,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_analytics')\n    .insert([analytics])\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport const updateVideoAnalytics = async (id: string, updates: Partial<VideoAnalytics>) => {\n  if (!hasValidCredentials) {\n    console.warn('Supabase not configured. Mock update for analytics:', id, updates)\n    return {\n      id,\n      ...updates,\n      updated_at: new Date().toISOString()\n    }\n  }\n\n  const { data, error } = await supabase\n    .from('video_analytics')\n    .update({ ...updates, updated_at: new Date().toISOString() })\n    .eq('id', id)\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,8CAA8C;AAC9C,MAAM,sBAAsB,gBAAgB,qCACjB,oBAAoB,qBACpB,YAAY,QAAQ,CAAC;AAEzC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAyB3C,MAAM,eAAe,OAAO,MAAc;IAC/C,IAAI,CAAC,qBAAqB;QACxB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,IAAI,UAAU,KAAK,GAAG;YACtB;YACA;YACA,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC;QAAC;YAAE;YAAM;QAAM;KAAE,EACxB,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,MAAM,uBAAuB,OAAO;IACzC,IAAI,CAAC,qBAAqB;QACxB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,IAAI,oBAAoB,KAAK,GAAG;YAChC,GAAG,SAAS;YACZ,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;QAAC;KAAU,EAClB,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,MAAM,uBAAuB,OAAO,IAAY;IACrD,IAAI,CAAC,qBAAqB;QACxB,QAAQ,IAAI,CAAC,uDAAuD,IAAI;QACxE,OAAO;YACL;YACA,GAAG,OAAO;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC;QAAE,GAAG,OAAO;QAAE,YAAY,IAAI,OAAO,WAAW;IAAG,GAC1D,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/lib/video-platforms.ts"], "sourcesContent": ["// Video platform detection and management\n\nexport type VideoPlatform = 'youtube' | 'vimeo' | 'wistia'\n\nexport interface VideoInfo {\n  platform: VideoPlatform\n  videoId: string\n  title?: string\n  duration?: number\n  thumbnailUrl?: string\n  embedUrl?: string\n}\n\nexport interface PlatformConfig {\n  name: string\n  pattern: RegExp[]\n  extractId: (url: string) => string | null\n  getEmbedUrl: (videoId: string) => string\n  getThumbnailUrl: (videoId: string) => string\n  getApiUrl?: (videoId: string) => string\n}\n\n// Platform configurations\nexport const platforms: Record<VideoPlatform, PlatformConfig> = {\n  youtube: {\n    name: 'YouTube',\n    pattern: [\n      /(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/|youtube\\.com\\/embed\\/)([a-zA-Z0-9_-]{11})/,\n      /youtube\\.com\\/watch\\?.*v=([a-zA-Z0-9_-]{11})/\n    ],\n    extractId: (url: string) => {\n      for (const pattern of platforms.youtube.pattern) {\n        const match = url.match(pattern)\n        if (match) return match[1]\n      }\n      return null\n    },\n    getEmbedUrl: (videoId: string) => `https://www.youtube.com/embed/${videoId}`,\n    getThumbnailUrl: (videoId: string) => `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n    getApiUrl: (videoId: string) => `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${process.env.YOUTUBE_API_KEY}&part=snippet,contentDetails`\n  },\n  \n  vimeo: {\n    name: 'Vimeo',\n    pattern: [\n      /vimeo\\.com\\/(\\d+)/,\n      /player\\.vimeo\\.com\\/video\\/(\\d+)/\n    ],\n    extractId: (url: string) => {\n      for (const pattern of platforms.vimeo.pattern) {\n        const match = url.match(pattern)\n        if (match) return match[1]\n      }\n      return null\n    },\n    getEmbedUrl: (videoId: string) => `https://player.vimeo.com/video/${videoId}`,\n    getThumbnailUrl: (videoId: string) => `https://vumbnail.com/${videoId}.jpg`,\n    getApiUrl: (videoId: string) => `https://vimeo.com/api/v2/video/${videoId}.json`\n  },\n  \n  wistia: {\n    name: 'Wistia',\n    pattern: [\n      /wistia\\.com\\/medias\\/([a-zA-Z0-9]+)/,\n      /wi\\.st\\/([a-zA-Z0-9]+)/,\n      /wistia\\.net\\/embed\\/iframe\\/([a-zA-Z0-9]+)/\n    ],\n    extractId: (url: string) => {\n      for (const pattern of platforms.wistia.pattern) {\n        const match = url.match(pattern)\n        if (match) return match[1]\n      }\n      return null\n    },\n    getEmbedUrl: (videoId: string) => `https://fast.wistia.net/embed/iframe/${videoId}`,\n    getThumbnailUrl: (videoId: string) => `https://embed-fastly.wistia.com/deliveries/${videoId}.jpg`,\n    getApiUrl: (videoId: string) => `https://api.wistia.com/v1/medias/${videoId}.json`\n  }\n}\n\n// Detect video platform and extract info\nexport const detectVideoPlatform = (url: string): VideoInfo | null => {\n  const cleanUrl = url.trim()\n  \n  for (const [platformKey, config] of Object.entries(platforms)) {\n    const platform = platformKey as VideoPlatform\n    const videoId = config.extractId(cleanUrl)\n    \n    if (videoId) {\n      return {\n        platform,\n        videoId,\n        embedUrl: config.getEmbedUrl(videoId),\n        thumbnailUrl: config.getThumbnailUrl(videoId)\n      }\n    }\n  }\n  \n  return null\n}\n\n// Fetch video metadata from platform APIs\nexport const fetchVideoMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {\n  try {\n    switch (videoInfo.platform) {\n      case 'youtube':\n        return await fetchYouTubeMetadata(videoInfo)\n      case 'vimeo':\n        return await fetchVimeoMetadata(videoInfo)\n      case 'wistia':\n        return await fetchWistiaMetadata(videoInfo)\n      default:\n        return videoInfo\n    }\n  } catch (error) {\n    console.error('Error fetching video metadata:', error)\n    return videoInfo\n  }\n}\n\n// YouTube metadata\nconst fetchYouTubeMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {\n  const apiKey = process.env.YOUTUBE_API_KEY\n  if (!apiKey) return videoInfo\n\n  const response = await fetch(\n    `https://www.googleapis.com/youtube/v3/videos?id=${videoInfo.videoId}&key=${apiKey}&part=snippet,contentDetails`\n  )\n  \n  if (!response.ok) return videoInfo\n  \n  const data = await response.json()\n  const video = data.items?.[0]\n  \n  if (!video) return videoInfo\n  \n  // Parse duration (PT4M13S format)\n  const duration = parseDuration(video.contentDetails?.duration)\n  \n  return {\n    ...videoInfo,\n    title: video.snippet?.title,\n    duration,\n    thumbnailUrl: video.snippet?.thumbnails?.maxres?.url || \n                  video.snippet?.thumbnails?.high?.url || \n                  videoInfo.thumbnailUrl\n  }\n}\n\n// Vimeo metadata\nconst fetchVimeoMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {\n  const response = await fetch(`https://vimeo.com/api/v2/video/${videoInfo.videoId}.json`)\n  \n  if (!response.ok) return videoInfo\n  \n  const data = await response.json()\n  const video = data[0]\n  \n  if (!video) return videoInfo\n  \n  return {\n    ...videoInfo,\n    title: video.title,\n    duration: video.duration,\n    thumbnailUrl: video.thumbnail_large || videoInfo.thumbnailUrl\n  }\n}\n\n// Wistia metadata\nconst fetchWistiaMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {\n  // Wistia API requires authentication, so we'll use oEmbed for basic info\n  const response = await fetch(\n    `https://fast.wistia.com/oembed?url=https://wistia.com/medias/${videoInfo.videoId}&format=json`\n  )\n  \n  if (!response.ok) return videoInfo\n  \n  const data = await response.json()\n  \n  return {\n    ...videoInfo,\n    title: data.title,\n    duration: data.duration,\n    thumbnailUrl: data.thumbnail_url || videoInfo.thumbnailUrl\n  }\n}\n\n// Parse YouTube duration format (PT4M13S)\nconst parseDuration = (duration: string): number => {\n  if (!duration) return 0\n  \n  const match = duration.match(/PT(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+)S)?/)\n  if (!match) return 0\n  \n  const hours = parseInt(match[1] || '0', 10)\n  const minutes = parseInt(match[2] || '0', 10)\n  const seconds = parseInt(match[3] || '0', 10)\n  \n  return hours * 3600 + minutes * 60 + seconds\n}\n\n// Validate video URL\nexport const isValidVideoUrl = (url: string): boolean => {\n  return detectVideoPlatform(url) !== null\n}\n\n// Get supported platforms list\nexport const getSupportedPlatforms = (): Array<{ key: VideoPlatform; name: string }> => {\n  return Object.entries(platforms).map(([key, config]) => ({\n    key: key as VideoPlatform,\n    name: config.name\n  }))\n}\n\n// Generate embed code\nexport const generateEmbedCode = (videoInfo: VideoInfo, options: {\n  width?: number\n  height?: number\n  autoplay?: boolean\n  controls?: boolean\n} = {}): string => {\n  const {\n    width = 640,\n    height = 360,\n    autoplay = false,\n    controls = false\n  } = options\n\n  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\n  \n  return `<iframe \n    src=\"${baseUrl}/embed/${videoInfo.videoId}?platform=${videoInfo.platform}&autoplay=${autoplay}&controls=${controls}\" \n    width=\"${width}\" \n    height=\"${height}\"\n    frameborder=\"0\"\n    allowfullscreen>\n  </iframe>`\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;;;;;AAuBnC,MAAM,YAAmD;IAC9D,SAAS;QACP,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,WAAW,CAAC;YACV,KAAK,MAAM,WAAW,UAAU,OAAO,CAAC,OAAO,CAAE;gBAC/C,MAAM,QAAQ,IAAI,KAAK,CAAC;gBACxB,IAAI,OAAO,OAAO,KAAK,CAAC,EAAE;YAC5B;YACA,OAAO;QACT;QACA,aAAa,CAAC,UAAoB,CAAC,8BAA8B,EAAE,SAAS;QAC5E,iBAAiB,CAAC,UAAoB,CAAC,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;QAC/F,WAAW,CAAC,UAAoB,CAAC,gDAAgD,EAAE,QAAQ,KAAK,EAAE,QAAQ,GAAG,CAAC,eAAe,CAAC,4BAA4B,CAAC;IAC7J;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,WAAW,CAAC;YACV,KAAK,MAAM,WAAW,UAAU,KAAK,CAAC,OAAO,CAAE;gBAC7C,MAAM,QAAQ,IAAI,KAAK,CAAC;gBACxB,IAAI,OAAO,OAAO,KAAK,CAAC,EAAE;YAC5B;YACA,OAAO;QACT;QACA,aAAa,CAAC,UAAoB,CAAC,+BAA+B,EAAE,SAAS;QAC7E,iBAAiB,CAAC,UAAoB,CAAC,qBAAqB,EAAE,QAAQ,IAAI,CAAC;QAC3E,WAAW,CAAC,UAAoB,CAAC,+BAA+B,EAAE,QAAQ,KAAK,CAAC;IAClF;IAEA,QAAQ;QACN,MAAM;QACN,SAAS;YACP;YACA;YACA;SACD;QACD,WAAW,CAAC;YACV,KAAK,MAAM,WAAW,UAAU,MAAM,CAAC,OAAO,CAAE;gBAC9C,MAAM,QAAQ,IAAI,KAAK,CAAC;gBACxB,IAAI,OAAO,OAAO,KAAK,CAAC,EAAE;YAC5B;YACA,OAAO;QACT;QACA,aAAa,CAAC,UAAoB,CAAC,qCAAqC,EAAE,SAAS;QACnF,iBAAiB,CAAC,UAAoB,CAAC,2CAA2C,EAAE,QAAQ,IAAI,CAAC;QACjG,WAAW,CAAC,UAAoB,CAAC,iCAAiC,EAAE,QAAQ,KAAK,CAAC;IACpF;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,WAAW,IAAI,IAAI;IAEzB,KAAK,MAAM,CAAC,aAAa,OAAO,IAAI,OAAO,OAAO,CAAC,WAAY;QAC7D,MAAM,WAAW;QACjB,MAAM,UAAU,OAAO,SAAS,CAAC;QAEjC,IAAI,SAAS;YACX,OAAO;gBACL;gBACA;gBACA,UAAU,OAAO,WAAW,CAAC;gBAC7B,cAAc,OAAO,eAAe,CAAC;YACvC;QACF;IACF;IAEA,OAAO;AACT;AAGO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,OAAQ,UAAU,QAAQ;YACxB,KAAK;gBACH,OAAO,MAAM,qBAAqB;YACpC,KAAK;gBACH,OAAO,MAAM,mBAAmB;YAClC,KAAK;gBACH,OAAO,MAAM,oBAAoB;YACnC;gBACE,OAAO;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAEA,mBAAmB;AACnB,MAAM,uBAAuB,OAAO;IAClC,MAAM,SAAS,QAAQ,GAAG,CAAC,eAAe;IAC1C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,MACrB,CAAC,gDAAgD,EAAE,UAAU,OAAO,CAAC,KAAK,EAAE,OAAO,4BAA4B,CAAC;IAGlH,IAAI,CAAC,SAAS,EAAE,EAAE,OAAO;IAEzB,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,MAAM,QAAQ,KAAK,KAAK,EAAE,CAAC,EAAE;IAE7B,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,WAAW,cAAc,MAAM,cAAc,EAAE;IAErD,OAAO;QACL,GAAG,SAAS;QACZ,OAAO,MAAM,OAAO,EAAE;QACtB;QACA,cAAc,MAAM,OAAO,EAAE,YAAY,QAAQ,OACnC,MAAM,OAAO,EAAE,YAAY,MAAM,OACjC,UAAU,YAAY;IACtC;AACF;AAEA,iBAAiB;AACjB,MAAM,qBAAqB,OAAO;IAChC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,UAAU,OAAO,CAAC,KAAK,CAAC;IAEvF,IAAI,CAAC,SAAS,EAAE,EAAE,OAAO;IAEzB,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,MAAM,QAAQ,IAAI,CAAC,EAAE;IAErB,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO;QACL,GAAG,SAAS;QACZ,OAAO,MAAM,KAAK;QAClB,UAAU,MAAM,QAAQ;QACxB,cAAc,MAAM,eAAe,IAAI,UAAU,YAAY;IAC/D;AACF;AAEA,kBAAkB;AAClB,MAAM,sBAAsB,OAAO;IACjC,yEAAyE;IACzE,MAAM,WAAW,MAAM,MACrB,CAAC,6DAA6D,EAAE,UAAU,OAAO,CAAC,YAAY,CAAC;IAGjG,IAAI,CAAC,SAAS,EAAE,EAAE,OAAO;IAEzB,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,OAAO;QACL,GAAG,SAAS;QACZ,OAAO,KAAK,KAAK;QACjB,UAAU,KAAK,QAAQ;QACvB,cAAc,KAAK,aAAa,IAAI,UAAU,YAAY;IAC5D;AACF;AAEA,0CAA0C;AAC1C,MAAM,gBAAgB,CAAC;IACrB,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,IAAI,KAAK;IACxC,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,IAAI,KAAK;IAC1C,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,IAAI,KAAK;IAE1C,OAAO,QAAQ,OAAO,UAAU,KAAK;AACvC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,oBAAoB,SAAS;AACtC;AAGO,MAAM,wBAAwB;IACnC,OAAO,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,GAAK,CAAC;YACvD,KAAK;YACL,MAAM,OAAO,IAAI;QACnB,CAAC;AACH;AAGO,MAAM,oBAAoB,CAAC,WAAsB,UAKpD,CAAC,CAAC;IACJ,MAAM,EACJ,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,WAAW,KAAK,EAChB,WAAW,KAAK,EACjB,GAAG;IAEJ,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;IAEpD,OAAO,CAAC;SACD,EAAE,QAAQ,OAAO,EAAE,UAAU,OAAO,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC,UAAU,EAAE,SAAS,UAAU,EAAE,SAAS;WAC5G,EAAE,MAAM;YACP,EAAE,OAAO;;;WAGV,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/lib/videos.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport { detectVideoPlatform, fetchVideoMetadata, generateEmbedCode, VideoInfo } from './video-platforms'\n\nexport interface Video {\n  id: string\n  user_id: string\n  title: string\n  video_url: string\n  video_id: string\n  platform: string\n  duration?: number\n  thumbnail_url?: string\n  embed_code?: string\n  is_active: boolean\n  custom_settings: any\n  created_at: string\n  updated_at: string\n}\n\nexport interface VideoStats {\n  total_views: number\n  total_leads: number\n  avg_completion: number\n  total_watch_time: number\n}\n\nexport interface CreateVideoData {\n  title: string\n  video_url: string\n  custom_settings?: any\n}\n\n// Create a new video\nexport const createVideo = async (userId: string, data: CreateVideoData): Promise<{ video: Video | null; error: string | null }> => {\n  try {\n    // Detect platform and extract video info\n    const videoInfo = detectVideoPlatform(data.video_url)\n    if (!videoInfo) {\n      return { video: null, error: 'Invalid video URL. Supported platforms: YouTube, Vimeo, Wistia' }\n    }\n\n    // Fetch metadata from platform\n    const enrichedVideoInfo = await fetchVideoMetadata(videoInfo)\n\n    // Generate embed code\n    const embedCode = generateEmbedCode(enrichedVideoInfo)\n\n    // Insert video into database\n    const { data: video, error } = await supabase\n      .from('videos')\n      .insert([{\n        user_id: userId,\n        title: data.title || enrichedVideoInfo.title || 'Untitled Video',\n        video_url: data.video_url,\n        video_id: enrichedVideoInfo.videoId,\n        platform: enrichedVideoInfo.platform,\n        duration: enrichedVideoInfo.duration,\n        thumbnail_url: enrichedVideoInfo.thumbnailUrl,\n        embed_code: embedCode,\n        custom_settings: data.custom_settings || {}\n      }])\n      .select()\n      .single()\n\n    if (error) {\n      return { video: null, error: error.message }\n    }\n\n    // Create default lead form for the video\n    await createDefaultLeadForm(video.id)\n\n    return { video, error: null }\n  } catch (error) {\n    return { video: null, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get user's videos\nexport const getUserVideos = async (userId: string): Promise<{ videos: Video[]; error: string | null }> => {\n  try {\n    const { data: videos, error } = await supabase\n      .from('videos')\n      .select('*')\n      .eq('user_id', userId)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      return { videos: [], error: error.message }\n    }\n\n    return { videos: videos || [], error: null }\n  } catch (error) {\n    return { videos: [], error: 'An unexpected error occurred' }\n  }\n}\n\n// Get video by ID\nexport const getVideo = async (videoId: string): Promise<{ video: Video | null; error: string | null }> => {\n  try {\n    const { data: video, error } = await supabase\n      .from('videos')\n      .select('*')\n      .eq('id', videoId)\n      .single()\n\n    if (error) {\n      return { video: null, error: error.message }\n    }\n\n    return { video, error: null }\n  } catch (error) {\n    return { video: null, error: 'An unexpected error occurred' }\n  }\n}\n\n// Update video\nexport const updateVideo = async (videoId: string, updates: Partial<Video>): Promise<{ video: Video | null; error: string | null }> => {\n  try {\n    const { data: video, error } = await supabase\n      .from('videos')\n      .update({ ...updates, updated_at: new Date().toISOString() })\n      .eq('id', videoId)\n      .select()\n      .single()\n\n    if (error) {\n      return { video: null, error: error.message }\n    }\n\n    return { video, error: null }\n  } catch (error) {\n    return { video: null, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete video\nexport const deleteVideo = async (videoId: string): Promise<{ error: string | null }> => {\n  try {\n    const { error } = await supabase\n      .from('videos')\n      .delete()\n      .eq('id', videoId)\n\n    return { error: error?.message || null }\n  } catch (error) {\n    return { error: 'An unexpected error occurred' }\n  }\n}\n\n// Get video statistics\nexport const getVideoStats = async (videoId: string): Promise<{ stats: VideoStats | null; error: string | null }> => {\n  try {\n    // Get view count\n    const { count: viewCount } = await supabase\n      .from('video_viewers')\n      .select('*', { count: 'exact', head: true })\n      .eq('video_id', videoId)\n\n    // Get analytics data\n    const { data: analytics, error: analyticsError } = await supabase\n      .from('video_analytics')\n      .select('watch_time, completion_percentage')\n      .eq('video_id', videoId)\n\n    if (analyticsError) {\n      return { stats: null, error: analyticsError.message }\n    }\n\n    // Calculate statistics\n    const totalViews = viewCount || 0\n    const totalLeads = totalViews // Each view is a lead (form submission)\n    const totalWatchTime = analytics?.reduce((sum, item) => sum + (item.watch_time || 0), 0) || 0\n    const avgCompletion = analytics?.length > 0 \n      ? analytics.reduce((sum, item) => sum + (item.completion_percentage || 0), 0) / analytics.length\n      : 0\n\n    const stats: VideoStats = {\n      total_views: totalViews,\n      total_leads: totalLeads,\n      avg_completion: Math.round(avgCompletion * 100) / 100,\n      total_watch_time: totalWatchTime\n    }\n\n    return { stats, error: null }\n  } catch (error) {\n    return { stats: null, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get user's overall statistics\nexport const getUserStats = async (userId: string): Promise<{ stats: any; error: string | null }> => {\n  try {\n    // Get all user videos\n    const { data: videos, error: videosError } = await supabase\n      .from('videos')\n      .select('id')\n      .eq('user_id', userId)\n\n    if (videosError) {\n      return { stats: null, error: videosError.message }\n    }\n\n    const videoIds = videos?.map(v => v.id) || []\n\n    if (videoIds.length === 0) {\n      return { \n        stats: { \n          total_videos: 0, \n          total_views: 0, \n          total_leads: 0, \n          avg_completion: 0,\n          total_watch_time: 0 \n        }, \n        error: null \n      }\n    }\n\n    // Get aggregated statistics\n    const { count: totalViews } = await supabase\n      .from('video_viewers')\n      .select('*', { count: 'exact', head: true })\n      .in('video_id', videoIds)\n\n    const { data: analytics } = await supabase\n      .from('video_analytics')\n      .select('watch_time, completion_percentage')\n      .in('video_id', videoIds)\n\n    const totalWatchTime = analytics?.reduce((sum, item) => sum + (item.watch_time || 0), 0) || 0\n    const avgCompletion = analytics?.length > 0 \n      ? analytics.reduce((sum, item) => sum + (item.completion_percentage || 0), 0) / analytics.length\n      : 0\n\n    const stats = {\n      total_videos: videos?.length || 0,\n      total_views: totalViews || 0,\n      total_leads: totalViews || 0,\n      avg_completion: Math.round(avgCompletion * 100) / 100,\n      total_watch_time: totalWatchTime\n    }\n\n    return { stats, error: null }\n  } catch (error) {\n    return { stats: null, error: 'An unexpected error occurred' }\n  }\n}\n\n// Create default lead form for video\nconst createDefaultLeadForm = async (videoId: string) => {\n  try {\n    await supabase\n      .from('lead_forms')\n      .insert([{\n        video_id: videoId,\n        title: 'Watch Video',\n        description: 'Enter your details to watch the video',\n        fields: [\n          { name: 'name', label: 'Full Name', required: true },\n          { name: 'email', label: 'Email Address', required: true }\n        ]\n      }])\n  } catch (error) {\n    console.error('Error creating default lead form:', error)\n  }\n}\n\n// Toggle video active status\nexport const toggleVideoStatus = async (videoId: string): Promise<{ video: Video | null; error: string | null }> => {\n  try {\n    // Get current status\n    const { data: currentVideo, error: fetchError } = await supabase\n      .from('videos')\n      .select('is_active')\n      .eq('id', videoId)\n      .single()\n\n    if (fetchError) {\n      return { video: null, error: fetchError.message }\n    }\n\n    // Toggle status\n    const { data: video, error } = await supabase\n      .from('videos')\n      .update({ \n        is_active: !currentVideo.is_active,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', videoId)\n      .select()\n      .single()\n\n    if (error) {\n      return { video: null, error: error.message }\n    }\n\n    return { video, error: null }\n  } catch (error) {\n    return { video: null, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAgCO,MAAM,cAAc,OAAO,QAAgB;IAChD,IAAI;QACF,yCAAyC;QACzC,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,SAAS;QACpD,IAAI,CAAC,WAAW;YACd,OAAO;gBAAE,OAAO;gBAAM,OAAO;YAAiE;QAChG;QAEA,+BAA+B;QAC/B,MAAM,oBAAoB,MAAM,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE;QAEnD,sBAAsB;QACtB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;QAEpC,6BAA6B;QAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,UACL,MAAM,CAAC;YAAC;gBACP,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI,kBAAkB,KAAK,IAAI;gBAChD,WAAW,KAAK,SAAS;gBACzB,UAAU,kBAAkB,OAAO;gBACnC,UAAU,kBAAkB,QAAQ;gBACpC,UAAU,kBAAkB,QAAQ;gBACpC,eAAe,kBAAkB,YAAY;gBAC7C,YAAY;gBACZ,iBAAiB,KAAK,eAAe,IAAI,CAAC;YAC5C;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,OAAO;gBAAE,OAAO;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC7C;QAEA,yCAAyC;QACzC,MAAM,sBAAsB,MAAM,EAAE;QAEpC,OAAO;YAAE;YAAO,OAAO;QAAK;IAC9B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;YAAM,OAAO;QAA+B;IAC9D;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,OAAO;gBAAE,QAAQ,EAAE;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC5C;QAEA,OAAO;YAAE,QAAQ,UAAU,EAAE;YAAE,OAAO;QAAK;IAC7C,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,QAAQ,EAAE;YAAE,OAAO;QAA+B;IAC7D;AACF;AAGO,MAAM,WAAW,OAAO;IAC7B,IAAI;QACF,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,SACT,MAAM;QAET,IAAI,OAAO;YACT,OAAO;gBAAE,OAAO;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC7C;QAEA,OAAO;YAAE;YAAO,OAAO;QAAK;IAC9B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;YAAM,OAAO;QAA+B;IAC9D;AACF;AAGO,MAAM,cAAc,OAAO,SAAiB;IACjD,IAAI;QACF,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,UACL,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC1D,EAAE,CAAC,MAAM,SACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,OAAO;gBAAE,OAAO;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC7C;QAEA,OAAO;YAAE;YAAO,OAAO;QAAK;IAC9B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;YAAM,OAAO;QAA+B;IAC9D;AACF;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,UACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,OAAO;YAAE,OAAO,OAAO,WAAW;QAAK;IACzC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,iBAAiB;QACjB,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACxC,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,YAAY;QAElB,qBAAqB;QACrB,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC9D,IAAI,CAAC,mBACL,MAAM,CAAC,qCACP,EAAE,CAAC,YAAY;QAElB,IAAI,gBAAgB;YAClB,OAAO;gBAAE,OAAO;gBAAM,OAAO,eAAe,OAAO;YAAC;QACtD;QAEA,uBAAuB;QACvB,MAAM,aAAa,aAAa;QAChC,MAAM,aAAa,WAAW,wCAAwC;;QACtE,MAAM,iBAAiB,WAAW,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM;QAC5F,MAAM,gBAAgB,WAAW,SAAS,IACtC,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,qBAAqB,IAAI,CAAC,GAAG,KAAK,UAAU,MAAM,GAC9F;QAEJ,MAAM,QAAoB;YACxB,aAAa;YACb,aAAa;YACb,gBAAgB,KAAK,KAAK,CAAC,gBAAgB,OAAO;YAClD,kBAAkB;QACpB;QAEA,OAAO;YAAE;YAAO,OAAO;QAAK;IAC9B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;YAAM,OAAO;QAA+B;IAC9D;AACF;AAGO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,sBAAsB;QACtB,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,UACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW;QAEjB,IAAI,aAAa;YACf,OAAO;gBAAE,OAAO;gBAAM,OAAO,YAAY,OAAO;YAAC;QACnD;QAEA,MAAM,WAAW,QAAQ,IAAI,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE;QAE7C,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,OAAO;gBACL,OAAO;oBACL,cAAc;oBACd,aAAa;oBACb,aAAa;oBACb,gBAAgB;oBAChB,kBAAkB;gBACpB;gBACA,OAAO;YACT;QACF;QAEA,4BAA4B;QAC5B,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,YAAY;QAElB,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACvC,IAAI,CAAC,mBACL,MAAM,CAAC,qCACP,EAAE,CAAC,YAAY;QAElB,MAAM,iBAAiB,WAAW,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM;QAC5F,MAAM,gBAAgB,WAAW,SAAS,IACtC,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,qBAAqB,IAAI,CAAC,GAAG,KAAK,UAAU,MAAM,GAC9F;QAEJ,MAAM,QAAQ;YACZ,cAAc,QAAQ,UAAU;YAChC,aAAa,cAAc;YAC3B,aAAa,cAAc;YAC3B,gBAAgB,KAAK,KAAK,CAAC,gBAAgB,OAAO;YAClD,kBAAkB;QACpB;QAEA,OAAO;YAAE;YAAO,OAAO;QAAK;IAC9B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;YAAM,OAAO;QAA+B;IAC9D;AACF;AAEA,qCAAqC;AACrC,MAAM,wBAAwB,OAAO;IACnC,IAAI;QACF,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,cACL,MAAM,CAAC;YAAC;gBACP,UAAU;gBACV,OAAO;gBACP,aAAa;gBACb,QAAQ;oBACN;wBAAE,MAAM;wBAAQ,OAAO;wBAAa,UAAU;oBAAK;oBACnD;wBAAE,MAAM;wBAAS,OAAO;wBAAiB,UAAU;oBAAK;iBACzD;YACH;SAAE;IACN,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;IACrD;AACF;AAGO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7D,IAAI,CAAC,UACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,SACT,MAAM;QAET,IAAI,YAAY;YACd,OAAO;gBAAE,OAAO;gBAAM,OAAO,WAAW,OAAO;YAAC;QAClD;QAEA,gBAAgB;QAChB,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,UACL,MAAM,CAAC;YACN,WAAW,CAAC,aAAa,SAAS;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,SACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,OAAO;gBAAE,OAAO;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC7C;QAEA,OAAO;YAAE;YAAO,OAAO;QAAK;IAC9B,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,OAAO;YAAM,OAAO;QAA+B;IAC9D;AACF", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter, usePathname } from 'next/navigation'\nimport Link from 'next/link'\nimport { \n  LayoutDashboard, \n  Video, \n  BarChart3, \n  Settings, \n  LogOut, \n  Menu, \n  X,\n  User\n} from 'lucide-react'\nimport { getCurrentUser, signOut, User as UserType } from '@/lib/auth'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [user, setUser] = useState<UserType | null>(null)\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const router = useRouter()\n  const pathname = usePathname()\n\n  useEffect(() => {\n    loadUser()\n  }, [])\n\n  const loadUser = async () => {\n    const currentUser = await getCurrentUser()\n    if (!currentUser) {\n      router.push('/login')\n    } else {\n      setUser(currentUser)\n    }\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/login')\n  }\n\n  const navigation = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: LayoutDashboard,\n      current: pathname === '/dashboard'\n    },\n    {\n      name: 'Videos',\n      href: '/dashboard/videos',\n      icon: Video,\n      current: pathname.startsWith('/dashboard/videos')\n    },\n    {\n      name: 'Analytics',\n      href: '/dashboard/analytics',\n      icon: BarChart3,\n      current: pathname.startsWith('/dashboard/analytics')\n    },\n    {\n      name: 'Settings',\n      href: '/dashboard/settings',\n      icon: Settings,\n      current: pathname.startsWith('/dashboard/settings')\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-slate-900\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-black/50\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 w-64 bg-slate-800 p-6\">\n          <div className=\"flex items-center justify-between mb-8\">\n            <h1 className=\"text-xl font-bold text-white\">VideoAnalytics</h1>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-white\"\n            >\n              <X className=\"w-6 h-6\" />\n            </button>\n          </div>\n          <SidebarContent navigation={navigation} onSignOut={handleSignOut} user={user} />\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:left-0 lg:w-64 lg:block\">\n        <div className=\"bg-slate-800 h-full p-6\">\n          <div className=\"mb-8\">\n            <h1 className=\"text-xl font-bold text-white\">VideoAnalytics</h1>\n            <p className=\"text-gray-400 text-sm mt-1\">SaaS Platform</p>\n          </div>\n          <SidebarContent navigation={navigation} onSignOut={handleSignOut} user={user} />\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"bg-slate-800 border-b border-slate-700 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden text-gray-400 hover:text-white\"\n            >\n              <Menu className=\"w-6 h-6\" />\n            </button>\n            \n            <div className=\"flex items-center space-x-4\">\n              {user && (\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center\">\n                    <User className=\"w-4 h-4 text-white\" />\n                  </div>\n                  <div className=\"hidden sm:block\">\n                    <p className=\"text-white text-sm font-medium\">{user.name}</p>\n                    <p className=\"text-gray-400 text-xs capitalize\">{user.plan} Plan</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarContent({ \n  navigation, \n  onSignOut, \n  user \n}: { \n  navigation: any[], \n  onSignOut: () => void,\n  user: UserType | null \n}) {\n  return (\n    <div className=\"flex flex-col h-full\">\n      <nav className=\"flex-1 space-y-2\">\n        {navigation.map((item) => {\n          const Icon = item.icon\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\n                item.current\n                  ? 'bg-purple-600 text-white'\n                  : 'text-gray-300 hover:bg-slate-700 hover:text-white'\n              }`}\n            >\n              <Icon className=\"w-5 h-5\" />\n              <span>{item.name}</span>\n            </Link>\n          )\n        })}\n      </nav>\n\n      {/* User info and sign out */}\n      <div className=\"border-t border-slate-700 pt-4\">\n        {user && (\n          <div className=\"mb-4 p-3 bg-slate-700 rounded-lg\">\n            <p className=\"text-white text-sm font-medium\">{user.name}</p>\n            <p className=\"text-gray-400 text-xs\">{user.email}</p>\n            <div className=\"mt-2\">\n              <span className={`inline-block px-2 py-1 text-xs rounded-full ${\n                user.plan === 'free' \n                  ? 'bg-gray-600 text-gray-200'\n                  : user.plan === 'pro'\n                  ? 'bg-purple-600 text-white'\n                  : 'bg-yellow-600 text-white'\n              }`}>\n                {user.plan.toUpperCase()} PLAN\n              </span>\n            </div>\n          </div>\n        )}\n        \n        <button\n          onClick={onSignOut}\n          className=\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-300 hover:bg-slate-700 hover:text-white transition-colors w-full\"\n        >\n          <LogOut className=\"w-5 h-5\" />\n          <span>Sign Out</span>\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAfA;;;;;;;AAqBe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,WAAW;QACf,MAAM,cAAc,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;QACvC,IAAI,CAAC,aAAa;YAChB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ;QACV;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD;QACZ,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,SAAS,SAAS,UAAU,CAAC;QAC/B;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,YAAS;YACf,SAAS,SAAS,UAAU,CAAC;QAC/B;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,SAAS,SAAS,UAAU,CAAC;QAC/B;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA4B,SAAS,IAAM,eAAe;;;;;;kCACzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;gCAAe,YAAY;gCAAY,WAAW;gCAAe,MAAM;;;;;;;;;;;;;;;;;;0BAK5E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAE5C,8OAAC;4BAAe,YAAY;4BAAY,WAAW;4BAAe,MAAM;;;;;;;;;;;;;;;;;0BAK5E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,8OAAC;oCAAI,WAAU;8CACZ,sBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAkC,KAAK,IAAI;;;;;;kEACxD,8OAAC;wDAAE,WAAU;;4DAAoC,KAAK,IAAI;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvE,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,UAAU,EACV,SAAS,EACT,IAAI,EAKL;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAC,uFAAuF,EACjG,KAAK,OAAO,GACR,6BACA,qDACJ;;0CAEF,8OAAC;gCAAK,WAAU;;;;;;0CAChB,8OAAC;0CAAM,KAAK,IAAI;;;;;;;uBATX,KAAK,IAAI;;;;;gBAYpB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAkC,KAAK,IAAI;;;;;;0CACxD,8OAAC;gCAAE,WAAU;0CAAyB,KAAK,KAAK;;;;;;0CAChD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAW,CAAC,4CAA4C,EAC5D,KAAK,IAAI,KAAK,SACV,8BACA,KAAK,IAAI,KAAK,QACd,6BACA,4BACJ;;wCACC,KAAK,IAAI,CAAC,WAAW;wCAAG;;;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/components/dashboard/VideoCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Play, MoreVertical, Eye, Users, TrendingUp, ExternalLink, Copy, Trash2 } from 'lucide-react'\n\ninterface Video {\n  id: string\n  title: string\n  platform: string\n  video_id: string\n  duration?: number\n  thumbnail_url?: string\n  is_active: boolean\n  created_at: string\n}\n\ninterface VideoCardProps {\n  video: Video\n  onUpdate: () => void\n}\n\nexport default function VideoCard({ video, onUpdate }: VideoCardProps) {\n  const [showMenu, setShowMenu] = useState(false)\n  const [stats, setStats] = useState({\n    views: 0,\n    leads: 0,\n    completion: 0\n  })\n\n  const formatDuration = (seconds?: number) => {\n    if (!seconds) return 'Unknown'\n    const minutes = Math.floor(seconds / 60)\n    const remainingSeconds = seconds % 60\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString()\n  }\n\n  const copyEmbedCode = () => {\n    const embedCode = `<iframe src=\"${window.location.origin}/embed/${video.id}\" width=\"640\" height=\"360\" frameborder=\"0\" allowfullscreen></iframe>`\n    navigator.clipboard.writeText(embedCode)\n    // You could add a toast notification here\n  }\n\n  const platformColors = {\n    youtube: 'bg-red-600',\n    vimeo: 'bg-blue-600',\n    wistia: 'bg-green-600'\n  }\n\n  return (\n    <div className=\"bg-slate-800 rounded-lg overflow-hidden border border-slate-700 hover:border-slate-600 transition-colors\">\n      {/* Video Thumbnail */}\n      <div className=\"relative aspect-video bg-slate-700\">\n        {video.thumbnail_url ? (\n          <img \n            src={video.thumbnail_url}\n            alt={video.title}\n            className=\"w-full h-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center\">\n            <Play className=\"w-12 h-12 text-gray-400\" />\n          </div>\n        )}\n        \n        {/* Platform Badge */}\n        <div className={`absolute top-2 left-2 ${platformColors[video.platform as keyof typeof platformColors] || 'bg-gray-600'} text-white text-xs px-2 py-1 rounded`}>\n          {video.platform.toUpperCase()}\n        </div>\n        \n        {/* Duration */}\n        {video.duration && (\n          <div className=\"absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded\">\n            {formatDuration(video.duration)}\n          </div>\n        )}\n        \n        {/* Status */}\n        <div className={`absolute top-2 right-2 w-3 h-3 rounded-full ${video.is_active ? 'bg-green-400' : 'bg-red-400'}`} />\n      </div>\n\n      {/* Video Info */}\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between mb-3\">\n          <h3 className=\"text-white font-medium text-sm line-clamp-2 flex-1\">\n            {video.title}\n          </h3>\n          <div className=\"relative ml-2\">\n            <button\n              onClick={() => setShowMenu(!showMenu)}\n              className=\"text-gray-400 hover:text-white p-1\"\n            >\n              <MoreVertical className=\"w-4 h-4\" />\n            </button>\n            \n            {showMenu && (\n              <div className=\"absolute right-0 top-8 bg-slate-700 border border-slate-600 rounded-lg shadow-lg z-10 min-w-[150px]\">\n                <button\n                  onClick={copyEmbedCode}\n                  className=\"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-300 hover:bg-slate-600 hover:text-white\"\n                >\n                  <Copy className=\"w-4 h-4\" />\n                  <span>Copy Embed</span>\n                </button>\n                <button\n                  onClick={() => window.open(`/embed/${video.id}`, '_blank')}\n                  className=\"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-300 hover:bg-slate-600 hover:text-white\"\n                >\n                  <ExternalLink className=\"w-4 h-4\" />\n                  <span>Preview</span>\n                </button>\n                <button\n                  onClick={() => {/* Add delete functionality */}}\n                  className=\"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-400 hover:bg-slate-600 hover:text-red-300\"\n                >\n                  <Trash2 className=\"w-4 h-4\" />\n                  <span>Delete</span>\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-3 gap-3 mb-3\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center text-blue-400 mb-1\">\n              <Eye className=\"w-4 h-4\" />\n            </div>\n            <div className=\"text-white text-sm font-medium\">{stats.views}</div>\n            <div className=\"text-gray-400 text-xs\">Views</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center text-green-400 mb-1\">\n              <Users className=\"w-4 h-4\" />\n            </div>\n            <div className=\"text-white text-sm font-medium\">{stats.leads}</div>\n            <div className=\"text-gray-400 text-xs\">Leads</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center text-purple-400 mb-1\">\n              <TrendingUp className=\"w-4 h-4\" />\n            </div>\n            <div className=\"text-white text-sm font-medium\">{stats.completion}%</div>\n            <div className=\"text-gray-400 text-xs\">Completion</div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-gray-400 text-xs\">\n          Created {formatDate(video.created_at)}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAqBe,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAkB;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,OAAO;QACP,OAAO;QACP,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,SAAS,OAAO;QACrB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACrE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,MAAM,gBAAgB;QACpB,MAAM,YAAY,CAAC,aAAa,EAAE,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,oEAAoE,CAAC;QAChJ,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,0CAA0C;IAC5C;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;oBACZ,MAAM,aAAa,iBAClB,8OAAC;wBACC,KAAK,MAAM,aAAa;wBACxB,KAAK,MAAM,KAAK;wBAChB,WAAU;;;;;6CAGZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAKpB,8OAAC;wBAAI,WAAW,CAAC,sBAAsB,EAAE,cAAc,CAAC,MAAM,QAAQ,CAAgC,IAAI,cAAc,qCAAqC,CAAC;kCAC3J,MAAM,QAAQ,CAAC,WAAW;;;;;;oBAI5B,MAAM,QAAQ,kBACb,8OAAC;wBAAI,WAAU;kCACZ,eAAe,MAAM,QAAQ;;;;;;kCAKlC,8OAAC;wBAAI,WAAW,CAAC,4CAA4C,EAAE,MAAM,SAAS,GAAG,iBAAiB,cAAc;;;;;;;;;;;;0BAIlH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,MAAM,KAAK;;;;;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,YAAY,CAAC;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;oCAGzB,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE;gDACjD,WAAU;;kEAEV,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDACC,SAAS,KAAqC;gDAC9C,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;kDAAkC,MAAM,KAAK;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDAAkC,MAAM,KAAK;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;4CAAkC,MAAM,UAAU;4CAAC;;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;4BAAwB;4BAC5B,WAAW,MAAM,UAAU;;;;;;;;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/components/dashboard/StatsCard.tsx"], "sourcesContent": ["interface StatsCardProps {\n  title: string\n  value: string | number\n  icon: React.ReactNode\n  color: 'blue' | 'green' | 'purple' | 'orange'\n}\n\nexport default function StatsCard({ title, value, icon, color }: StatsCardProps) {\n  const colorClasses = {\n    blue: 'from-blue-600 to-blue-700',\n    green: 'from-green-600 to-green-700',\n    purple: 'from-purple-600 to-purple-700',\n    orange: 'from-orange-600 to-orange-700'\n  }\n\n  return (\n    <div className=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-gray-400 text-sm font-medium\">{title}</p>\n          <p className=\"text-2xl font-bold text-white mt-1\">{value}</p>\n        </div>\n        <div className={`bg-gradient-to-r ${colorClasses[color]} p-3 rounded-lg`}>\n          <div className=\"text-white\">\n            {icon}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOe,SAAS,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAkB;IAC7E,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;8BAErD,8OAAC;oBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAM,CAAC,eAAe,CAAC;8BACtE,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/components/dashboard/AddVideoModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { X, Plus, Youtube, Video, Play } from 'lucide-react'\nimport { createVideo } from '@/lib/videos'\nimport { isValidVideoUrl, getSupportedPlatforms } from '@/lib/video-platforms'\n\nconst videoSchema = z.object({\n  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),\n  video_url: z.string().url('Please enter a valid URL').refine(\n    (url) => isValidVideoUrl(url),\n    'Please enter a valid YouTube, Vimeo, or Wistia URL'\n  )\n})\n\ntype VideoFormData = z.infer<typeof videoSchema>\n\ninterface AddVideoModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onVideoAdded: () => void\n  userId: string\n}\n\nexport default function AddVideoModal({ isOpen, onClose, onVideoAdded, userId }: AddVideoModalProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [error, setError] = useState('')\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    watch\n  } = useForm<VideoFormData>({\n    resolver: zodResolver(videoSchema)\n  })\n\n  const watchedUrl = watch('video_url')\n  const supportedPlatforms = getSupportedPlatforms()\n\n  const onSubmit = async (data: VideoFormData) => {\n    setIsSubmitting(true)\n    setError('')\n\n    try {\n      const { video, error } = await createVideo(userId, data)\n      \n      if (error) {\n        setError(error)\n      } else if (video) {\n        reset()\n        onVideoAdded()\n        onClose()\n      }\n    } catch (err) {\n      setError('An unexpected error occurred')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const handleClose = () => {\n    reset()\n    setError('')\n    onClose()\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div \n        className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n        onClick={handleClose}\n      />\n      \n      {/* Modal */}\n      <div className=\"relative bg-slate-800 rounded-lg p-6 w-full max-w-md mx-4 border border-slate-700\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-xl font-bold text-white\">Add New Video</h2>\n          <button\n            onClick={handleClose}\n            className=\"text-gray-400 hover:text-white\"\n          >\n            <X className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          {/* Error Message */}\n          {error && (\n            <div className=\"bg-red-500/20 border border-red-500/50 rounded-lg p-3\">\n              <p className=\"text-red-200 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Video URL Field */}\n          <div>\n            <label className=\"block text-white text-sm font-medium mb-2\">\n              Video URL\n            </label>\n            <div className=\"relative\">\n              <Video className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n              <input\n                {...register('video_url')}\n                type=\"url\"\n                className=\"w-full pl-10 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                placeholder=\"https://www.youtube.com/watch?v=...\"\n              />\n            </div>\n            {errors.video_url && (\n              <p className=\"text-red-400 text-sm mt-1\">{errors.video_url.message}</p>\n            )}\n            \n            {/* Supported Platforms */}\n            <div className=\"mt-2\">\n              <p className=\"text-gray-400 text-xs mb-2\">Supported platforms:</p>\n              <div className=\"flex space-x-2\">\n                {supportedPlatforms.map((platform) => (\n                  <span\n                    key={platform.key}\n                    className=\"bg-slate-700 text-gray-300 text-xs px-2 py-1 rounded\"\n                  >\n                    {platform.name}\n                  </span>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Title Field */}\n          <div>\n            <label className=\"block text-white text-sm font-medium mb-2\">\n              Video Title\n            </label>\n            <input\n              {...register('title')}\n              type=\"text\"\n              className=\"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n              placeholder=\"Enter a title for your video\"\n            />\n            {errors.title && (\n              <p className=\"text-red-400 text-sm mt-1\">{errors.title.message}</p>\n            )}\n            <p className=\"text-gray-400 text-xs mt-1\">\n              Leave empty to auto-detect from video metadata\n            </p>\n          </div>\n\n          {/* URL Preview */}\n          {watchedUrl && isValidVideoUrl(watchedUrl) && (\n            <div className=\"bg-slate-700 rounded-lg p-3\">\n              <div className=\"flex items-center space-x-2 text-green-400 text-sm\">\n                <Play className=\"w-4 h-4\" />\n                <span>Valid video URL detected</span>\n              </div>\n            </div>\n          )}\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              className=\"flex-1 bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2\"\n            >\n              {isSubmitting ? (\n                <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n              ) : (\n                <>\n                  <Plus className=\"w-5 h-5\" />\n                  <span>Add Video</span>\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,cAAc,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,KAAK;IACvD,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,4BAA4B,MAAM,CAC1D,CAAC,MAAQ,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,MACzB;AAEJ;AAWe,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAsB;IACjG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,aAAa,MAAM;IACzB,MAAM,qBAAqB,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;IAE/C,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;YAEnD,IAAI,OAAO;gBACT,SAAS;YACX,OAAO,IAAI,OAAO;gBAChB;gBACA;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB;QACA,SAAS;QACT;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAC7C,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;4BAE/C,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;0CAKzC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAG7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDACE,GAAG,SAAS,YAAY;gDACzB,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;oCAGf,OAAO,SAAS,kBACf,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,SAAS,CAAC,OAAO;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,8OAAC;wDAEC,WAAU;kEAET,SAAS,IAAI;uDAHT,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;0CAW3B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAG7D,8OAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;oCAEb,OAAO,KAAK,kBACX,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;kDAEhE,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;4BAM3C,cAAc,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,6BAC7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;0CAMZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,6BACC,8OAAC;4CAAI,WAAU;;;;;iEAEf;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 2097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Plus, Video, Users, TrendingUp, Clock, Eye, BarChart3 } from 'lucide-react'\nimport { getCurrentUser, User } from '@/lib/auth'\nimport { getUserVideos, getUserStats, Video } from '@/lib/videos'\nimport DashboardLayout from '@/components/dashboard/DashboardLayout'\nimport VideoCard from '@/components/dashboard/VideoCard'\nimport StatsCard from '@/components/dashboard/StatsCard'\nimport AddVideoModal from '@/components/dashboard/AddVideoModal'\n\nexport default function DashboardPage() {\n  const [user, setUser] = useState<User | null>(null)\n  const [videos, setVideos] = useState<Video[]>([])\n  const [stats, setStats] = useState<any>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [showAddModal, setShowAddModal] = useState(false)\n  const router = useRouter()\n\n  useEffect(() => {\n    loadDashboardData()\n  }, [])\n\n  const loadDashboardData = async () => {\n    try {\n      // Check authentication\n      const currentUser = await getCurrentUser()\n      if (!currentUser) {\n        router.push('/login')\n        return\n      }\n\n      setUser(currentUser)\n\n      // Load user's videos and stats\n      const [videosResult, statsResult] = await Promise.all([\n        getUserVideos(currentUser.id),\n        getUserStats(currentUser.id)\n      ])\n\n      if (videosResult.error) {\n        console.error('Error loading videos:', videosResult.error)\n      } else {\n        setVideos(videosResult.videos)\n      }\n\n      if (statsResult.error) {\n        console.error('Error loading stats:', statsResult.error)\n      } else {\n        setStats(statsResult.stats)\n      }\n    } catch (error) {\n      console.error('Error loading dashboard:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleVideoAdded = () => {\n    setShowAddModal(false)\n    loadDashboardData() // Refresh data\n  }\n\n  const formatTime = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600)\n    const minutes = Math.floor((seconds % 3600) / 60)\n    if (hours > 0) {\n      return `${hours}h ${minutes}m`\n    }\n    return `${minutes}m`\n  }\n\n  if (isLoading) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full animate-spin\" />\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-8\">\n        {/* Header */}\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-white\">\n              Welcome back, {user?.name}!\n            </h1>\n            <p className=\"text-gray-400 mt-1\">\n              Here's what's happening with your videos\n            </p>\n          </div>\n          <button\n            onClick={() => setShowAddModal(true)}\n            className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 flex items-center space-x-2\"\n          >\n            <Plus className=\"w-5 h-5\" />\n            <span>Add Video</span>\n          </button>\n        </div>\n\n        {/* Stats Cards */}\n        {stats && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <StatsCard\n              title=\"Total Videos\"\n              value={stats.total_videos}\n              icon={<Video className=\"w-6 h-6\" />}\n              color=\"blue\"\n            />\n            <StatsCard\n              title=\"Total Views\"\n              value={stats.total_views}\n              icon={<Eye className=\"w-6 h-6\" />}\n              color=\"green\"\n            />\n            <StatsCard\n              title=\"Total Leads\"\n              value={stats.total_leads}\n              icon={<Users className=\"w-6 h-6\" />}\n              color=\"purple\"\n            />\n            <StatsCard\n              title=\"Avg. Completion\"\n              value={`${stats.avg_completion}%`}\n              icon={<TrendingUp className=\"w-6 h-6\" />}\n              color=\"orange\"\n            />\n          </div>\n        )}\n\n        {/* Videos Section */}\n        <div>\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-2xl font-bold text-white\">Your Videos</h2>\n            {videos.length > 0 && (\n              <div className=\"text-gray-400 text-sm\">\n                {videos.length} video{videos.length !== 1 ? 's' : ''}\n              </div>\n            )}\n          </div>\n\n          {videos.length === 0 ? (\n            /* Empty State */\n            <div className=\"bg-slate-800 rounded-lg p-12 text-center\">\n              <Video className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-white mb-2\">\n                No videos yet\n              </h3>\n              <p className=\"text-gray-400 mb-6 max-w-md mx-auto\">\n                Add your first video to start tracking analytics and generating leads. \n                We support YouTube, Vimeo, and Wistia.\n              </p>\n              <button\n                onClick={() => setShowAddModal(true)}\n                className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 flex items-center space-x-2 mx-auto\"\n              >\n                <Plus className=\"w-5 h-5\" />\n                <span>Add Your First Video</span>\n              </button>\n            </div>\n          ) : (\n            /* Videos Grid */\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {videos.map((video) => (\n                <VideoCard\n                  key={video.id}\n                  video={video}\n                  onUpdate={loadDashboardData}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Quick Actions */}\n        {videos.length > 0 && (\n          <div className=\"bg-slate-800 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Quick Actions</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <button\n                onClick={() => setShowAddModal(true)}\n                className=\"bg-slate-700 hover:bg-slate-600 text-white p-4 rounded-lg transition-colors flex items-center space-x-3\"\n              >\n                <Plus className=\"w-5 h-5 text-purple-400\" />\n                <span>Add Another Video</span>\n              </button>\n              <button\n                onClick={() => router.push('/dashboard/analytics')}\n                className=\"bg-slate-700 hover:bg-slate-600 text-white p-4 rounded-lg transition-colors flex items-center space-x-3\"\n              >\n                <BarChart3 className=\"w-5 h-5 text-green-400\" />\n                <span>View Analytics</span>\n              </button>\n              <button\n                onClick={() => router.push('/dashboard/settings')}\n                className=\"bg-slate-700 hover:bg-slate-600 text-white p-4 rounded-lg transition-colors flex items-center space-x-3\"\n              >\n                <Users className=\"w-5 h-5 text-blue-400\" />\n                <span>Account Settings</span>\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Add Video Modal */}\n      <AddVideoModal\n        isOpen={showAddModal}\n        onClose={() => setShowAddModal(false)}\n        onVideoAdded={handleVideoAdded}\n        userId={user?.id || ''}\n      />\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,uBAAuB;YACvB,MAAM,cAAc,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;YACvC,IAAI,CAAC,aAAa;gBAChB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,QAAQ;YAER,+BAA+B;YAC/B,MAAM,CAAC,cAAc,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpD,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,EAAE;gBAC5B,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,EAAE;aAC5B;YAED,IAAI,aAAa,KAAK,EAAE;gBACtB,QAAQ,KAAK,CAAC,yBAAyB,aAAa,KAAK;YAC3D,OAAO;gBACL,UAAU,aAAa,MAAM;YAC/B;YAEA,IAAI,YAAY,KAAK,EAAE;gBACrB,QAAQ,KAAK,CAAC,wBAAwB,YAAY,KAAK;YACzD,OAAO;gBACL,SAAS,YAAY,KAAK;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,oBAAoB,eAAe;;IACrC;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChC;QACA,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,kJAAA,CAAA,UAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC,kJAAA,CAAA,UAAe;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAAgC;4CAC7B,MAAM;4CAAK;;;;;;;kDAE5B,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAIpC,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAKT,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4IAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,MAAM,YAAY;gCACzB,oBAAM,8OAAC,oHAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACvB,OAAM;;;;;;0CAER,8OAAC,4IAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,MAAM,WAAW;gCACxB,oBAAM,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCACrB,OAAM;;;;;;0CAER,8OAAC,4IAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,MAAM,WAAW;gCACxB,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACvB,OAAM;;;;;;0CAER,8OAAC,4IAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,GAAG,MAAM,cAAc,CAAC,CAAC,CAAC;gCACjC,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAC5B,OAAM;;;;;;;;;;;;kCAMZ,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;oCAC7C,OAAO,MAAM,GAAG,mBACf,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,MAAM;4CAAC;4CAAO,OAAO,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;4BAKvD,OAAO,MAAM,KAAK,IACjB,eAAe,iBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oHAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAInD,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;uCAIV,eAAe,iBACf,8OAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,4IAAA,CAAA,UAAS;wCAER,OAAO;wCACP,UAAU;uCAFL,MAAM,EAAE;;;;;;;;;;;;;;;;oBAUtB,OAAO,MAAM,GAAG,mBACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC,gJAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,cAAc;gBACd,QAAQ,MAAM,MAAM;;;;;;;;;;;;AAI5B", "debugId": null}}]}