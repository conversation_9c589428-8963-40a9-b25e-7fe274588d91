'use client'

import { AlertTriangle, ExternalLink } from 'lucide-react'

export default function ConfigNotice() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const isConfigured = supabaseUrl && supabaseUrl !== 'https://placeholder.supabase.co' && supabaseUrl.includes('supabase.co')

  if (isConfigured) return null

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <div className="bg-yellow-500/90 backdrop-blur-sm text-black p-4 rounded-lg shadow-lg border border-yellow-400">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-semibold text-sm mb-1">Demo Mode</h3>
            <p className="text-xs mb-2">
              Supabase is not configured. Video tracking will use mock data.
            </p>
            <a
              href="https://supabase.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-xs font-medium hover:underline"
            >
              Set up Supabase
              <ExternalLink className="w-3 h-3 ml-1" />
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
