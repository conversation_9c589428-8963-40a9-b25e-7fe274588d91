const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzcwMzAsImV4cCI6MjA2NjYxMzAzMH0.wU8DuhUgDnIlx6WyqU36sfC6-Wq0dc4Z2Ik1xns3oUk'

const supabase = createClient(supabaseUrl, anonKey)

async function testCurrentApp() {
  console.log('🧪 Testing Current Application Status...')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  
  const tests = []
  
  // Test 1: Landing Page (should work)
  tests.push({
    name: 'Landing Page',
    url: 'http://localhost:3000',
    expected: 'Working - Shows SaaS landing page',
    status: '✅'
  })
  
  // Test 2: Authentication (will fail - no users table)
  try {
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'password123'
    })
    
    if (error) {
      tests.push({
        name: 'User Registration',
        expected: 'Will fail - users table missing',
        actual: `Error: ${error.message}`,
        status: '❌'
      })
    }
  } catch (err) {
    tests.push({
      name: 'User Registration',
      expected: 'Will fail - users table missing',
      actual: `Error: ${err.message}`,
      status: '❌'
    })
  }
  
  // Test 3: Video Analytics (partially working - old structure)
  try {
    const { data: viewers, error: viewerError } = await supabase
      .from('video_viewers')
      .select('*')
      .limit(1)
    
    if (!viewerError) {
      tests.push({
        name: 'Video Viewers (Old Structure)',
        expected: 'Working - old single-video structure',
        actual: `Found ${viewers?.length || 0} viewers`,
        status: '⚠️'
      })
    }
  } catch (err) {
    tests.push({
      name: 'Video Viewers',
      expected: 'Should work',
      actual: `Error: ${err.message}`,
      status: '❌'
    })
  }
  
  // Test 4: Video Management (will fail - no videos table)
  try {
    const { data: videos, error: videoError } = await supabase
      .from('videos')
      .select('*')
      .limit(1)
    
    if (videoError) {
      tests.push({
        name: 'Video Management',
        expected: 'Will fail - videos table missing',
        actual: `Error: ${videoError.message}`,
        status: '❌'
      })
    }
  } catch (err) {
    tests.push({
      name: 'Video Management',
      expected: 'Will fail - videos table missing',
      actual: `Error: ${err.message}`,
      status: '❌'
    })
  }
  
  // Test 5: Embed functionality (will fail - no proper structure)
  tests.push({
    name: 'Embed System',
    url: 'http://localhost:3000/embed/test-id',
    expected: 'Will fail - no videos table',
    status: '❌'
  })
  
  console.log('\n📋 Application Test Results:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  
  tests.forEach(test => {
    console.log(`${test.status} ${test.name}`)
    if (test.url) console.log(`   URL: ${test.url}`)
    if (test.expected) console.log(`   Expected: ${test.expected}`)
    if (test.actual) console.log(`   Actual: ${test.actual}`)
    console.log('')
  })
  
  console.log('📊 Summary:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('✅ WORKING:')
  console.log('   • Landing page (SaaS marketing site)')
  console.log('   • Old video analytics (single video structure)')
  console.log('   • Basic Supabase connection')
  console.log('')
  console.log('❌ NOT WORKING:')
  console.log('   • User registration/login (no users table)')
  console.log('   • Dashboard (requires authentication)')
  console.log('   • Video management (no videos table)')
  console.log('   • Embed system (no proper video structure)')
  console.log('   • Multi-tenant features (no SaaS structure)')
  console.log('')
  console.log('⚠️  PARTIALLY WORKING:')
  console.log('   • Video tracking (old single-video structure)')
  console.log('   • Analytics display (old admin page)')
  console.log('')
  console.log('🔧 SOLUTION:')
  console.log('   Run the database migration to enable full SaaS functionality!')
}

testCurrentApp()
