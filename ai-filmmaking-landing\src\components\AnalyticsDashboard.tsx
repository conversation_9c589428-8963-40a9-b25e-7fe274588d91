'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Users, Play, Clock, TrendingUp } from 'lucide-react'

interface ViewerAnalytics {
  id: string
  name: string
  email: string
  created_at: string
  watch_time: number
  completion_percentage: number
  current_position: number
  session_start: string
  session_end?: string
}

export default function AnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<ViewerAnalytics[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalViewers: 0,
    averageWatchTime: 0,
    averageCompletion: 0,
    totalWatchTime: 0
  })

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const fetchAnalytics = async () => {
    try {
      const { data, error } = await supabase
        .from('video_analytics')
        .select(`
          *,
          video_viewers (
            name,
            email,
            created_at
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      const formattedData: ViewerAnalytics[] = data.map((item: any) => ({
        id: item.id,
        name: item.video_viewers.name,
        email: item.video_viewers.email,
        created_at: item.video_viewers.created_at,
        watch_time: item.watch_time,
        completion_percentage: item.completion_percentage,
        current_position: item.current_position,
        session_start: item.session_start,
        session_end: item.session_end
      }))

      setAnalytics(formattedData)

      // Calculate stats
      const totalViewers = formattedData.length
      const totalWatchTime = formattedData.reduce((sum, item) => sum + item.watch_time, 0)
      const averageWatchTime = totalViewers > 0 ? totalWatchTime / totalViewers : 0
      const averageCompletion = totalViewers > 0 
        ? formattedData.reduce((sum, item) => sum + item.completion_percentage, 0) / totalViewers 
        : 0

      setStats({
        totalViewers,
        averageWatchTime,
        averageCompletion,
        totalWatchTime
      })

    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading analytics...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Video Analytics Dashboard</h1>
        
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-slate-800 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Viewers</p>
                <p className="text-2xl font-bold text-white">{stats.totalViewers}</p>
              </div>
              <Users className="w-8 h-8 text-blue-400" />
            </div>
          </div>
          
          <div className="bg-slate-800 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Avg Watch Time</p>
                <p className="text-2xl font-bold text-white">{formatTime(Math.floor(stats.averageWatchTime))}</p>
              </div>
              <Clock className="w-8 h-8 text-green-400" />
            </div>
          </div>
          
          <div className="bg-slate-800 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Avg Completion</p>
                <p className="text-2xl font-bold text-white">{stats.averageCompletion.toFixed(1)}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-400" />
            </div>
          </div>
          
          <div className="bg-slate-800 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Watch Time</p>
                <p className="text-2xl font-bold text-white">{formatTime(stats.totalWatchTime)}</p>
              </div>
              <Play className="w-8 h-8 text-red-400" />
            </div>
          </div>
        </div>

        {/* Analytics Table */}
        <div className="bg-slate-800 rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-slate-700">
            <h2 className="text-xl font-semibold text-white">Viewer Details</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Viewer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Watch Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Completion
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Last Position
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Started At
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-700">
                {analytics.map((viewer) => (
                  <tr key={viewer.id} className="hover:bg-slate-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-white">{viewer.name}</div>
                        <div className="text-sm text-gray-400">{viewer.email}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      {formatTime(viewer.watch_time)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-20 bg-slate-600 rounded-full h-3 mr-3">
                          <div
                            className={`h-3 rounded-full transition-all duration-300 ${
                              viewer.completion_percentage >= 75 ? 'bg-green-400' :
                              viewer.completion_percentage >= 50 ? 'bg-yellow-400' :
                              viewer.completion_percentage >= 25 ? 'bg-orange-400' : 'bg-red-400'
                            }`}
                            style={{ width: `${Math.min(viewer.completion_percentage, 100)}%` }}
                          />
                        </div>
                        <span className="text-sm text-white font-medium">
                          {viewer.completion_percentage.toFixed(1)}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      {formatTime(viewer.current_position)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                      {formatDate(viewer.session_start)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
