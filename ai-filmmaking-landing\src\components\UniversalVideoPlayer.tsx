'use client'

import { useEffect, useRef, useState } from 'react'
import { insertVideoAnalytics, updateVideoAnalytics } from '@/lib/supabase'

interface UniversalVideoPlayerProps {
  videoId: string
  platform: 'youtube' | 'vimeo' | 'wistia'
  viewerId: string
  duration?: number
  onProgress?: (data: {
    currentTime: number
    duration: number
    completionPercentage: number
  }) => void
}

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
    Vimeo: any
    Wistia: any
    _wq: any[]
  }
}

export default function UniversalVideoPlayer({
  videoId,
  platform,
  viewerId,
  duration: initialDuration,
  onProgress
}: UniversalVideoPlayerProps) {
  const playerRef = useRef<any>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [analyticsId, setAnalyticsId] = useState<string | null>(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(initialDuration || 0)
  const [totalWatchTime, setTotalWatchTime] = useState(0)
  const [maxPositionReached, setMaxPositionReached] = useState(0)
  const [watchedSegments, setWatchedSegments] = useState<Set<number>>(new Set())
  const [sessionStart] = useState(new Date().toISOString())
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastUpdateTime = useRef<number>(Date.now())

  useEffect(() => {
    initializePlayer()
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      if (analyticsId) {
        updateAnalytics()
      }
    }
  }, [])

  const initializePlayer = async () => {
    switch (platform) {
      case 'youtube':
        await initializeYouTubePlayer()
        break
      case 'vimeo':
        await initializeVimeoPlayer()
        break
      case 'wistia':
        await initializeWistiaPlayer()
        break
    }
  }

  const initializeYouTubePlayer = async () => {
    if (!window.YT) {
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
    }

    const initPlayer = () => {
      if (window.YT && window.YT.Player) {
        playerRef.current = new window.YT.Player(containerRef.current, {
          height: '100%',
          width: '100%',
          videoId: videoId,
          playerVars: {
            autoplay: 1,
            controls: 0,
            disablekb: 1,
            fs: 0,
            iv_load_policy: 3,
            modestbranding: 1,
            playsinline: 1,
            rel: 0,
            showinfo: 0
          },
          events: {
            onReady: onPlayerReady,
            onStateChange: onPlayerStateChange
          }
        })
      }
    }

    if (window.YT && window.YT.Player) {
      initPlayer()
    } else {
      window.onYouTubeIframeAPIReady = initPlayer
    }
  }

  const initializeVimeoPlayer = async () => {
    // Load Vimeo Player API
    if (!window.Vimeo) {
      const script = document.createElement('script')
      script.src = 'https://player.vimeo.com/api/player.js'
      document.head.appendChild(script)
      
      await new Promise((resolve) => {
        script.onload = resolve
      })
    }

    const iframe = document.createElement('iframe')
    iframe.src = `https://player.vimeo.com/video/${videoId}?autoplay=1&controls=0&title=0&byline=0&portrait=0`
    iframe.width = '100%'
    iframe.height = '100%'
    iframe.frameBorder = '0'
    iframe.allow = 'autoplay; fullscreen'
    
    if (containerRef.current) {
      containerRef.current.appendChild(iframe)
    }

    playerRef.current = new window.Vimeo.Player(iframe)
    
    playerRef.current.on('loaded', onPlayerReady)
    playerRef.current.on('play', () => startTracking())
    playerRef.current.on('pause', () => stopTracking())
    playerRef.current.on('ended', () => stopTracking())
    playerRef.current.on('timeupdate', (data: any) => {
      setCurrentTime(data.seconds)
      setMaxPositionReached(prev => Math.max(prev, data.seconds))
    })
  }

  const initializeWistiaPlayer = async () => {
    // Load Wistia API
    if (!window.Wistia) {
      const script = document.createElement('script')
      script.src = 'https://fast.wistia.com/assets/external/E-v1.js'
      document.head.appendChild(script)
      
      await new Promise((resolve) => {
        script.onload = resolve
      })
    }

    window._wq = window._wq || []
    window._wq.push({
      id: videoId,
      onReady: (video: any) => {
        playerRef.current = video
        setDuration(video.duration())
        onPlayerReady()
        
        video.bind('play', () => startTracking())
        video.bind('pause', () => stopTracking())
        video.bind('end', () => stopTracking())
        video.bind('timechange', (t: number) => {
          setCurrentTime(t)
          setMaxPositionReached(prev => Math.max(prev, t))
        })
      }
    })

    // Create Wistia embed
    const wistiaDiv = document.createElement('div')
    wistiaDiv.className = `wistia_embed wistia_async_${videoId}`
    wistiaDiv.style.width = '100%'
    wistiaDiv.style.height = '100%'
    
    if (containerRef.current) {
      containerRef.current.appendChild(wistiaDiv)
    }
  }

  const onPlayerReady = async () => {
    // Get duration for YouTube
    if (platform === 'youtube' && playerRef.current?.getDuration) {
      setDuration(playerRef.current.getDuration())
    }

    // Create analytics record
    try {
      const analytics = await insertVideoAnalytics({
        video_id: videoId,
        viewer_id: viewerId,
        watch_time: 0,
        total_duration: duration || 0,
        completion_percentage: 0,
        current_position: 0,
        max_position_reached: 0,
        segments_watched: [],
        session_start: sessionStart
      })
      setAnalyticsId(analytics.id)
    } catch (error) {
      console.error('Error creating analytics record:', error)
    }
  }

  const onPlayerStateChange = (event: any) => {
    if (platform === 'youtube') {
      const state = event.data
      if (state === window.YT.PlayerState.PLAYING) {
        startTracking()
      } else if (state === window.YT.PlayerState.PAUSED || 
                 state === window.YT.PlayerState.ENDED) {
        stopTracking()
      }
    }
  }

  const startTracking = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    lastUpdateTime.current = Date.now()

    intervalRef.current = setInterval(() => {
      const now = Date.now()
      const timeSinceLastUpdate = (now - lastUpdateTime.current) / 1000

      // Get current time based on platform
      let currentPlayerTime = 0
      if (platform === 'youtube' && playerRef.current?.getCurrentTime) {
        currentPlayerTime = playerRef.current.getCurrentTime()
      } else if (platform === 'vimeo' && playerRef.current?.getCurrentTime) {
        playerRef.current.getCurrentTime().then((time: number) => {
          currentPlayerTime = time
        })
      } else if (platform === 'wistia' && playerRef.current?.time) {
        currentPlayerTime = playerRef.current.time()
      }

      // Only count as watch time if playing continuously
      if (timeSinceLastUpdate >= 0.8 && timeSinceLastUpdate <= 1.5) {
        setTotalWatchTime(prev => prev + timeSinceLastUpdate)
        
        // Track segments
        const currentSegment = Math.floor(currentPlayerTime / 10)
        setWatchedSegments(prev => new Set([...prev, currentSegment]))
      }

      setCurrentTime(currentPlayerTime)
      setMaxPositionReached(prev => Math.max(prev, currentPlayerTime))
      lastUpdateTime.current = now

      // Update analytics every 10 seconds
      if (Math.floor(totalWatchTime) % 10 === 0 && Math.floor(totalWatchTime) > 0) {
        updateAnalytics()
      }

      // Call progress callback
      if (onProgress && duration > 0) {
        onProgress({
          currentTime: currentPlayerTime,
          duration,
          completionPercentage: (maxPositionReached / duration) * 100
        })
      }
    }, 1000)
  }

  const stopTracking = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    updateAnalytics()
  }

  const updateAnalytics = async () => {
    if (!analyticsId || !duration) return

    const progressCompletion = (maxPositionReached / duration) * 100
    const totalSegments = Math.ceil(duration / 10)
    const engagementCompletion = (watchedSegments.size / totalSegments) * 100
    const finalCompletion = Math.max(progressCompletion, engagementCompletion)

    try {
      await updateVideoAnalytics(analyticsId, {
        watch_time: Math.floor(totalWatchTime),
        completion_percentage: Math.min(finalCompletion, 100),
        current_position: Math.floor(currentTime),
        max_position_reached: Math.floor(maxPositionReached),
        segments_watched: Array.from(watchedSegments),
        session_end: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error updating analytics:', error)
    }
  }

  return (
    <div className="relative w-full h-full bg-black">
      <div ref={containerRef} className="w-full h-full" />
      
      {/* Progress indicator */}
      <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-1">
        <span className="text-white text-sm">
          {Math.floor(totalWatchTime / 60)}:{(Math.floor(totalWatchTime) % 60).toString().padStart(2, '0')} | 
          {duration > 0 ? ` ${((maxPositionReached / duration) * 100).toFixed(1)}%` : ''}
        </span>
      </div>
    </div>
  )
}
