// Video platform detection and management

export type VideoPlatform = 'youtube' | 'vimeo' | 'wistia'

export interface VideoInfo {
  platform: VideoPlatform
  videoId: string
  title?: string
  duration?: number
  thumbnailUrl?: string
  embedUrl?: string
}

export interface PlatformConfig {
  name: string
  pattern: RegExp[]
  extractId: (url: string) => string | null
  getEmbedUrl: (videoId: string) => string
  getThumbnailUrl: (videoId: string) => string
  getApiUrl?: (videoId: string) => string
}

// Platform configurations
export const platforms: Record<VideoPlatform, PlatformConfig> = {
  youtube: {
    name: 'YouTube',
    pattern: [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
      /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/
    ],
    extractId: (url: string) => {
      for (const pattern of platforms.youtube.pattern) {
        const match = url.match(pattern)
        if (match) return match[1]
      }
      return null
    },
    getEmbedUrl: (videoId: string) => `https://www.youtube.com/embed/${videoId}`,
    getThumbnailUrl: (videoId: string) => `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
    getApiUrl: (videoId: string) => `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${process.env.YOUTUBE_API_KEY}&part=snippet,contentDetails`
  },
  
  vimeo: {
    name: 'Vimeo',
    pattern: [
      /vimeo\.com\/(\d+)/,
      /player\.vimeo\.com\/video\/(\d+)/
    ],
    extractId: (url: string) => {
      for (const pattern of platforms.vimeo.pattern) {
        const match = url.match(pattern)
        if (match) return match[1]
      }
      return null
    },
    getEmbedUrl: (videoId: string) => `https://player.vimeo.com/video/${videoId}`,
    getThumbnailUrl: (videoId: string) => `https://vumbnail.com/${videoId}.jpg`,
    getApiUrl: (videoId: string) => `https://vimeo.com/api/v2/video/${videoId}.json`
  },
  
  wistia: {
    name: 'Wistia',
    pattern: [
      /wistia\.com\/medias\/([a-zA-Z0-9]+)/,
      /wi\.st\/([a-zA-Z0-9]+)/,
      /wistia\.net\/embed\/iframe\/([a-zA-Z0-9]+)/
    ],
    extractId: (url: string) => {
      for (const pattern of platforms.wistia.pattern) {
        const match = url.match(pattern)
        if (match) return match[1]
      }
      return null
    },
    getEmbedUrl: (videoId: string) => `https://fast.wistia.net/embed/iframe/${videoId}`,
    getThumbnailUrl: (videoId: string) => `https://embed-fastly.wistia.com/deliveries/${videoId}.jpg`,
    getApiUrl: (videoId: string) => `https://api.wistia.com/v1/medias/${videoId}.json`
  }
}

// Detect video platform and extract info
export const detectVideoPlatform = (url: string): VideoInfo | null => {
  const cleanUrl = url.trim()
  
  for (const [platformKey, config] of Object.entries(platforms)) {
    const platform = platformKey as VideoPlatform
    const videoId = config.extractId(cleanUrl)
    
    if (videoId) {
      return {
        platform,
        videoId,
        embedUrl: config.getEmbedUrl(videoId),
        thumbnailUrl: config.getThumbnailUrl(videoId)
      }
    }
  }
  
  return null
}

// Fetch video metadata from platform APIs
export const fetchVideoMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {
  try {
    switch (videoInfo.platform) {
      case 'youtube':
        return await fetchYouTubeMetadata(videoInfo)
      case 'vimeo':
        return await fetchVimeoMetadata(videoInfo)
      case 'wistia':
        return await fetchWistiaMetadata(videoInfo)
      default:
        return videoInfo
    }
  } catch (error) {
    console.error('Error fetching video metadata:', error)
    return videoInfo
  }
}

// YouTube metadata
const fetchYouTubeMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {
  const apiKey = process.env.YOUTUBE_API_KEY
  if (!apiKey) return videoInfo

  const response = await fetch(
    `https://www.googleapis.com/youtube/v3/videos?id=${videoInfo.videoId}&key=${apiKey}&part=snippet,contentDetails`
  )
  
  if (!response.ok) return videoInfo
  
  const data = await response.json()
  const video = data.items?.[0]
  
  if (!video) return videoInfo
  
  // Parse duration (PT4M13S format)
  const duration = parseDuration(video.contentDetails?.duration)
  
  return {
    ...videoInfo,
    title: video.snippet?.title,
    duration,
    thumbnailUrl: video.snippet?.thumbnails?.maxres?.url || 
                  video.snippet?.thumbnails?.high?.url || 
                  videoInfo.thumbnailUrl
  }
}

// Vimeo metadata
const fetchVimeoMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {
  const response = await fetch(`https://vimeo.com/api/v2/video/${videoInfo.videoId}.json`)
  
  if (!response.ok) return videoInfo
  
  const data = await response.json()
  const video = data[0]
  
  if (!video) return videoInfo
  
  return {
    ...videoInfo,
    title: video.title,
    duration: video.duration,
    thumbnailUrl: video.thumbnail_large || videoInfo.thumbnailUrl
  }
}

// Wistia metadata
const fetchWistiaMetadata = async (videoInfo: VideoInfo): Promise<VideoInfo> => {
  // Wistia API requires authentication, so we'll use oEmbed for basic info
  const response = await fetch(
    `https://fast.wistia.com/oembed?url=https://wistia.com/medias/${videoInfo.videoId}&format=json`
  )
  
  if (!response.ok) return videoInfo
  
  const data = await response.json()
  
  return {
    ...videoInfo,
    title: data.title,
    duration: data.duration,
    thumbnailUrl: data.thumbnail_url || videoInfo.thumbnailUrl
  }
}

// Parse YouTube duration format (PT4M13S)
const parseDuration = (duration: string): number => {
  if (!duration) return 0
  
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
  if (!match) return 0
  
  const hours = parseInt(match[1] || '0', 10)
  const minutes = parseInt(match[2] || '0', 10)
  const seconds = parseInt(match[3] || '0', 10)
  
  return hours * 3600 + minutes * 60 + seconds
}

// Validate video URL
export const isValidVideoUrl = (url: string): boolean => {
  return detectVideoPlatform(url) !== null
}

// Get supported platforms list
export const getSupportedPlatforms = (): Array<{ key: VideoPlatform; name: string }> => {
  return Object.entries(platforms).map(([key, config]) => ({
    key: key as VideoPlatform,
    name: config.name
  }))
}

// Generate embed code
export const generateEmbedCode = (videoInfo: VideoInfo, options: {
  width?: number
  height?: number
  autoplay?: boolean
  controls?: boolean
} = {}): string => {
  const {
    width = 640,
    height = 360,
    autoplay = false,
    controls = false
  } = options

  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  
  return `<iframe 
    src="${baseUrl}/embed/${videoInfo.videoId}?platform=${videoInfo.platform}&autoplay=${autoplay}&controls=${controls}" 
    width="${width}" 
    height="${height}"
    frameborder="0"
    allowfullscreen>
  </iframe>`
}
