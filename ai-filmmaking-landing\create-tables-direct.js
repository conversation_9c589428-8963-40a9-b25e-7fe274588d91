const https = require('https')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzcwMzAsImV4cCI6MjA2NjYxMzAzMH0.wU8DuhUgDnIlx6WyqU36sfC6-Wq0dc4Z2Ik1xns3oUk'

async function createTablesDirectly() {
  console.log('🚀 Attempting to create tables using direct HTTP requests...')
  
  const sqlCommands = [
    // Create video_viewers table
    `CREATE TABLE IF NOT EXISTS video_viewers (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      email VARCHAR(255) NOT NULL UNIQUE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );`,
    
    // Create video_analytics table
    `CREATE TABLE IF NOT EXISTS video_analytics (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      viewer_id UUID REFERENCES video_viewers(id) ON DELETE CASCADE,
      video_id VARCHAR(255) NOT NULL,
      watch_time INTEGER DEFAULT 0,
      total_duration INTEGER NOT NULL,
      completion_percentage DECIMAL(5,2) DEFAULT 0.00,
      current_position INTEGER DEFAULT 0,
      session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      session_end TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );`,
    
    // Create indexes
    `CREATE INDEX IF NOT EXISTS idx_video_analytics_viewer_id ON video_analytics(viewer_id);`,
    `CREATE INDEX IF NOT EXISTS idx_video_analytics_video_id ON video_analytics(video_id);`,
    `CREATE INDEX IF NOT EXISTS idx_video_viewers_email ON video_viewers(email);`,
    
    // Enable RLS
    `ALTER TABLE video_viewers ENABLE ROW LEVEL SECURITY;`,
    `ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;`,
    
    // Create policies
    `CREATE POLICY IF NOT EXISTS "Allow public insert on video_viewers" ON video_viewers FOR INSERT WITH CHECK (true);`,
    `CREATE POLICY IF NOT EXISTS "Allow public select on video_viewers" ON video_viewers FOR SELECT USING (true);`,
    `CREATE POLICY IF NOT EXISTS "Allow public insert on video_analytics" ON video_analytics FOR INSERT WITH CHECK (true);`,
    `CREATE POLICY IF NOT EXISTS "Allow public update on video_analytics" ON video_analytics FOR UPDATE USING (true);`,
    `CREATE POLICY IF NOT EXISTS "Allow public select on video_analytics" ON video_analytics FOR SELECT USING (true);`
  ]
  
  try {
    // Try using the PostgREST API to execute SQL
    const url = `${supabaseUrl}/rest/v1/rpc/exec_sql`
    
    for (let i = 0; i < sqlCommands.length; i++) {
      const sql = sqlCommands[i]
      console.log(`📝 Executing command ${i + 1}/${sqlCommands.length}...`)
      
      const postData = JSON.stringify({ sql: sql })
      
      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseKey}`,
          'apikey': supabaseKey,
          'Content-Length': Buffer.byteLength(postData)
        }
      }
      
      try {
        const response = await makeRequest(url, options, postData)
        console.log(`   ✅ Command ${i + 1} executed successfully`)
      } catch (error) {
        console.log(`   ❌ Command ${i + 1} failed:`, error.message)
        if (i === 0) {
          // If the first command fails, stop and show manual instructions
          console.log('\n❌ Cannot create tables automatically with current permissions')
          showManualInstructions()
          return
        }
      }
    }
    
    console.log('\n🎉 Database setup completed successfully!')
    console.log('✅ All tables and policies have been created')
    console.log('🚀 Your AI Filmmaking landing page is ready to use!')
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    showManualInstructions()
  }
}

function makeRequest(url, options, postData) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(data)
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${data}`))
        }
      })
    })
    
    req.on('error', (error) => {
      reject(error)
    })
    
    if (postData) {
      req.write(postData)
    }
    
    req.end()
  })
}

function showManualInstructions() {
  console.log('\n📋 MANUAL SETUP REQUIRED:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('1. 🌐 Go to your Supabase Dashboard:')
  console.log('   https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
  console.log('')
  console.log('2. 📄 Copy and paste this SQL code:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log(`
-- Create video_viewers table
CREATE TABLE video_viewers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create video_analytics table
CREATE TABLE video_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  viewer_id UUID REFERENCES video_viewers(id) ON DELETE CASCADE,
  video_id VARCHAR(255) NOT NULL,
  watch_time INTEGER DEFAULT 0,
  total_duration INTEGER NOT NULL,
  completion_percentage DECIMAL(5,2) DEFAULT 0.00,
  current_position INTEGER DEFAULT 0,
  session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_video_analytics_viewer_id ON video_analytics(viewer_id);
CREATE INDEX idx_video_analytics_video_id ON video_analytics(video_id);
CREATE INDEX idx_video_viewers_email ON video_viewers(email);

-- Enable Row Level Security (RLS)
ALTER TABLE video_viewers ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for public access
CREATE POLICY "Allow public insert on video_viewers" ON video_viewers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on video_viewers" ON video_viewers
  FOR SELECT USING (true);

CREATE POLICY "Allow public insert on video_analytics" ON video_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public update on video_analytics" ON video_analytics
  FOR UPDATE USING (true);

CREATE POLICY "Allow public select on video_analytics" ON video_analytics
  FOR SELECT USING (true);
`)
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('3. ▶️  Click the "Run" button to execute the SQL')
  console.log('4. 🔄 Refresh your landing page at http://localhost:3000')
  console.log('5. ✅ The yellow configuration notice should disappear')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
}

createTablesDirectly()
