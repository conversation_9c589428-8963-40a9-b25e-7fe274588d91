const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzcwMzAsImV4cCI6MjA2NjYxMzAzMH0.wU8DuhUgDnIlx6WyqU36sfC6-Wq0dc4Z2Ik1xns3oUk'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
  console.log('🔍 Testing Supabase connection and permissions...')
  
  try {
    // Test 1: Check if we can access any existing tables
    console.log('📋 Checking existing tables...')
    
    // Try to access video_viewers table
    const { data: viewersData, error: viewersError } = await supabase
      .from('video_viewers')
      .select('count(*)')
      .limit(1)
    
    if (viewersError) {
      console.log('❌ video_viewers table:', viewersError.message)
      if (viewersError.code === '42P01') {
        console.log('   → Table does not exist')
      }
    } else {
      console.log('✅ video_viewers table exists and accessible')
      console.log('   → Current count:', viewersData)
    }
    
    // Try to access video_analytics table
    const { data: analyticsData, error: analyticsError } = await supabase
      .from('video_analytics')
      .select('count(*)')
      .limit(1)
    
    if (analyticsError) {
      console.log('❌ video_analytics table:', analyticsError.message)
      if (analyticsError.code === '42P01') {
        console.log('   → Table does not exist')
      }
    } else {
      console.log('✅ video_analytics table exists and accessible')
      console.log('   → Current count:', analyticsData)
    }
    
    // Test 2: Try to create tables using SQL
    console.log('\n🛠️ Attempting to create tables...')
    
    // Try using different RPC methods
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS video_viewers (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
    
    // Method 1: Try exec_sql
    const { data: rpcData, error: rpcError } = await supabase.rpc('exec_sql', {
      sql: createTableSQL
    })
    
    if (rpcError) {
      console.log('❌ RPC exec_sql failed:', rpcError.message)
    } else {
      console.log('✅ RPC exec_sql succeeded')
    }
    
    // Method 2: Try direct SQL execution
    const { data: sqlData, error: sqlError } = await supabase
      .rpc('sql', { query: createTableSQL })
    
    if (sqlError) {
      console.log('❌ Direct SQL failed:', sqlError.message)
    } else {
      console.log('✅ Direct SQL succeeded')
    }
    
    console.log('\n📋 Summary:')
    console.log('- Supabase connection: ✅ Working')
    console.log('- Table creation: ❌ Requires manual setup')
    console.log('- Recommended action: Use Supabase Dashboard SQL Editor')
    
  } catch (error) {
    console.error('❌ Connection test failed:', error.message)
  }
}

testConnection()
