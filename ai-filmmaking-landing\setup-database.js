const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzcwMzAsImV4cCI6MjA2NjYxMzAzMH0.wU8DuhUgDnIlx6WyqU36sfC6-Wq0dc4Z2Ik1xns3oUk'

const supabase = createClient(supabaseUrl, supabaseKey)

async function setupDatabase() {
  console.log('🚀 Setting up Supabase database...')
  
  try {
    // Create video_viewers table
    console.log('📝 Creating video_viewers table...')
    const { error: viewersError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS video_viewers (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name VA<PERSON>HA<PERSON>(255) NOT NULL,
          email VARCHAR(255) NOT NULL UNIQUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    })
    
    if (viewersError) {
      console.log('ℹ️  video_viewers table might already exist or using direct SQL...')
    } else {
      console.log('✅ video_viewers table created successfully')
    }

    // Create video_analytics table
    console.log('📝 Creating video_analytics table...')
    const { error: analyticsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS video_analytics (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          viewer_id UUID REFERENCES video_viewers(id) ON DELETE CASCADE,
          video_id VARCHAR(255) NOT NULL,
          watch_time INTEGER DEFAULT 0,
          total_duration INTEGER NOT NULL,
          completion_percentage DECIMAL(5,2) DEFAULT 0.00,
          current_position INTEGER DEFAULT 0,
          session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          session_end TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    })
    
    if (analyticsError) {
      console.log('ℹ️  video_analytics table might already exist or using direct SQL...')
    } else {
      console.log('✅ video_analytics table created successfully')
    }

    // Test connection by trying to select from video_viewers
    console.log('🔍 Testing database connection...')
    const { data, error } = await supabase
      .from('video_viewers')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      console.log('⚠️  Database tables might not exist yet. Please run the SQL manually.')
      console.log('📋 Copy and paste the contents of database-schema.sql into your Supabase SQL Editor')
      console.log('🔗 Go to: https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
    } else {
      console.log('✅ Database connection successful!')
      console.log('🎉 Setup complete! Your AI Filmmaking landing page is ready.')
    }

  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    console.log('\n📋 Manual Setup Instructions:')
    console.log('1. Go to: https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
    console.log('2. Copy and paste the contents of database-schema.sql')
    console.log('3. Click "Run" to execute the SQL')
    console.log('4. Restart your development server: npm run dev')
  }
}

setupDatabase()
