import { supabase } from './supabase'
import { detectVideoPlatform, fetchVideoMetadata, generateEmbedCode, VideoInfo } from './video-platforms'

export interface Video {
  id: string
  user_id: string
  title: string
  video_url: string
  video_id: string
  platform: string
  duration?: number
  thumbnail_url?: string
  embed_code?: string
  is_active: boolean
  custom_settings: any
  created_at: string
  updated_at: string
}

export interface VideoStats {
  total_views: number
  total_leads: number
  avg_completion: number
  total_watch_time: number
}

export interface CreateVideoData {
  title: string
  video_url: string
  custom_settings?: any
}

// Create a new video
export const createVideo = async (userId: string, data: CreateVideoData): Promise<{ video: Video | null; error: string | null }> => {
  try {
    // Detect platform and extract video info
    const videoInfo = detectVideoPlatform(data.video_url)
    if (!videoInfo) {
      return { video: null, error: 'Invalid video URL. Supported platforms: YouTube, Vimeo, Wistia' }
    }

    // Fetch metadata from platform
    const enrichedVideoInfo = await fetchVideoMetadata(videoInfo)

    // Generate embed code
    const embedCode = generateEmbedCode(enrichedVideoInfo)

    // Insert video into database
    const { data: video, error } = await supabase
      .from('videos')
      .insert([{
        user_id: userId,
        title: data.title || enrichedVideoInfo.title || 'Untitled Video',
        video_url: data.video_url,
        video_id: enrichedVideoInfo.videoId,
        platform: enrichedVideoInfo.platform,
        duration: enrichedVideoInfo.duration,
        thumbnail_url: enrichedVideoInfo.thumbnailUrl,
        embed_code: embedCode,
        custom_settings: data.custom_settings || {}
      }])
      .select()
      .single()

    if (error) {
      return { video: null, error: error.message }
    }

    // Create default lead form for the video
    await createDefaultLeadForm(video.id)

    return { video, error: null }
  } catch (error) {
    return { video: null, error: 'An unexpected error occurred' }
  }
}

// Get user's videos
export const getUserVideos = async (userId: string): Promise<{ videos: Video[]; error: string | null }> => {
  try {
    const { data: videos, error } = await supabase
      .from('videos')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      return { videos: [], error: error.message }
    }

    return { videos: videos || [], error: null }
  } catch (error) {
    return { videos: [], error: 'An unexpected error occurred' }
  }
}

// Get video by ID
export const getVideo = async (videoId: string): Promise<{ video: Video | null; error: string | null }> => {
  try {
    const { data: video, error } = await supabase
      .from('videos')
      .select('*')
      .eq('id', videoId)
      .single()

    if (error) {
      return { video: null, error: error.message }
    }

    return { video, error: null }
  } catch (error) {
    return { video: null, error: 'An unexpected error occurred' }
  }
}

// Update video
export const updateVideo = async (videoId: string, updates: Partial<Video>): Promise<{ video: Video | null; error: string | null }> => {
  try {
    const { data: video, error } = await supabase
      .from('videos')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', videoId)
      .select()
      .single()

    if (error) {
      return { video: null, error: error.message }
    }

    return { video, error: null }
  } catch (error) {
    return { video: null, error: 'An unexpected error occurred' }
  }
}

// Delete video
export const deleteVideo = async (videoId: string): Promise<{ error: string | null }> => {
  try {
    const { error } = await supabase
      .from('videos')
      .delete()
      .eq('id', videoId)

    return { error: error?.message || null }
  } catch (error) {
    return { error: 'An unexpected error occurred' }
  }
}

// Get video statistics
export const getVideoStats = async (videoId: string): Promise<{ stats: VideoStats | null; error: string | null }> => {
  try {
    // Get view count
    const { count: viewCount } = await supabase
      .from('video_viewers')
      .select('*', { count: 'exact', head: true })
      .eq('video_id', videoId)

    // Get analytics data
    const { data: analytics, error: analyticsError } = await supabase
      .from('video_analytics')
      .select('watch_time, completion_percentage')
      .eq('video_id', videoId)

    if (analyticsError) {
      return { stats: null, error: analyticsError.message }
    }

    // Calculate statistics
    const totalViews = viewCount || 0
    const totalLeads = totalViews // Each view is a lead (form submission)
    const totalWatchTime = analytics?.reduce((sum, item) => sum + (item.watch_time || 0), 0) || 0
    const avgCompletion = analytics?.length > 0 
      ? analytics.reduce((sum, item) => sum + (item.completion_percentage || 0), 0) / analytics.length
      : 0

    const stats: VideoStats = {
      total_views: totalViews,
      total_leads: totalLeads,
      avg_completion: Math.round(avgCompletion * 100) / 100,
      total_watch_time: totalWatchTime
    }

    return { stats, error: null }
  } catch (error) {
    return { stats: null, error: 'An unexpected error occurred' }
  }
}

// Get user's overall statistics
export const getUserStats = async (userId: string): Promise<{ stats: any; error: string | null }> => {
  try {
    // Get all user videos
    const { data: videos, error: videosError } = await supabase
      .from('videos')
      .select('id')
      .eq('user_id', userId)

    if (videosError) {
      return { stats: null, error: videosError.message }
    }

    const videoIds = videos?.map(v => v.id) || []

    if (videoIds.length === 0) {
      return { 
        stats: { 
          total_videos: 0, 
          total_views: 0, 
          total_leads: 0, 
          avg_completion: 0,
          total_watch_time: 0 
        }, 
        error: null 
      }
    }

    // Get aggregated statistics
    const { count: totalViews } = await supabase
      .from('video_viewers')
      .select('*', { count: 'exact', head: true })
      .in('video_id', videoIds)

    const { data: analytics } = await supabase
      .from('video_analytics')
      .select('watch_time, completion_percentage')
      .in('video_id', videoIds)

    const totalWatchTime = analytics?.reduce((sum, item) => sum + (item.watch_time || 0), 0) || 0
    const avgCompletion = analytics?.length > 0 
      ? analytics.reduce((sum, item) => sum + (item.completion_percentage || 0), 0) / analytics.length
      : 0

    const stats = {
      total_videos: videos?.length || 0,
      total_views: totalViews || 0,
      total_leads: totalViews || 0,
      avg_completion: Math.round(avgCompletion * 100) / 100,
      total_watch_time: totalWatchTime
    }

    return { stats, error: null }
  } catch (error) {
    return { stats: null, error: 'An unexpected error occurred' }
  }
}

// Create default lead form for video
const createDefaultLeadForm = async (videoId: string) => {
  try {
    await supabase
      .from('lead_forms')
      .insert([{
        video_id: videoId,
        title: 'Watch Video',
        description: 'Enter your details to watch the video',
        fields: [
          { name: 'name', label: 'Full Name', required: true },
          { name: 'email', label: 'Email Address', required: true }
        ]
      }])
  } catch (error) {
    console.error('Error creating default lead form:', error)
  }
}

// Toggle video active status
export const toggleVideoStatus = async (videoId: string): Promise<{ video: Video | null; error: string | null }> => {
  try {
    // Get current status
    const { data: currentVideo, error: fetchError } = await supabase
      .from('videos')
      .select('is_active')
      .eq('id', videoId)
      .single()

    if (fetchError) {
      return { video: null, error: fetchError.message }
    }

    // Toggle status
    const { data: video, error } = await supabase
      .from('videos')
      .update({ 
        is_active: !currentVideo.is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', videoId)
      .select()
      .single()

    if (error) {
      return { video: null, error: error.message }
    }

    return { video, error: null }
  } catch (error) {
    return { video: null, error: 'An unexpected error occurred' }
  }
}
