const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNzAzMCwiZXhwIjoyMDY2NjEzMDMwfQ.I7iuGrqM94GqY5Ya54rlJ1Bo_V2B3LxyM-7yAlN5dcQ'

const supabase = createClient(supabaseUrl, serviceRoleKey)

async function checkCurrentDatabase() {
  console.log('🔍 Checking current database state...')
  
  try {
    // Check what tables currently exist
    const existingTables = ['video_viewers', 'video_analytics', 'users', 'videos', 'lead_forms', 'embed_analytics']
    
    console.log('\n📋 Current Database Status:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    
    for (const table of existingTables) {
      try {
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true })
        
        if (error) {
          if (error.code === '42P01') {
            console.log(`❌ ${table.padEnd(20)} - Table does not exist`)
          } else {
            console.log(`⚠️  ${table.padEnd(20)} - Error: ${error.message}`)
          }
        } else {
          console.log(`✅ ${table.padEnd(20)} - Exists (${count || 0} records)`)
        }
      } catch (err) {
        console.log(`❌ ${table.padEnd(20)} - Error: ${err.message}`)
      }
    }
    
    // Check if we have the old single-video structure
    const { data: oldViewers, error: oldError } = await supabase
      .from('video_viewers')
      .select('*')
      .limit(1)
    
    if (!oldError && oldViewers) {
      console.log('\n📊 Found existing single-video data:')
      const { count: viewerCount } = await supabase
        .from('video_viewers')
        .select('*', { count: 'exact', head: true })
      
      const { count: analyticsCount } = await supabase
        .from('video_analytics')
        .select('*', { count: 'exact', head: true })
      
      console.log(`• ${viewerCount || 0} video viewers`)
      console.log(`• ${analyticsCount || 0} analytics records`)
      console.log('\n💡 Migration needed: Convert single-video to multi-tenant SaaS')
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message)
  }
}

checkCurrentDatabase()
