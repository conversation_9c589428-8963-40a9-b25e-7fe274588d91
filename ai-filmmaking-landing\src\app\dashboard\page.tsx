'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Plus, Video, Users, TrendingUp, Clock, Eye, BarChart3 } from 'lucide-react'
import { getCurrentUser, User } from '@/lib/auth'
import { getUserVideos, getUserStats, Video } from '@/lib/videos'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import VideoCard from '@/components/dashboard/VideoCard'
import StatsCard from '@/components/dashboard/StatsCard'
import AddVideoModal from '@/components/dashboard/AddVideoModal'

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [videos, setVideos] = useState<Video[]>([])
  const [stats, setStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const router = useRouter()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // Check authentication
      const currentUser = await getCurrentUser()
      if (!currentUser) {
        router.push('/login')
        return
      }

      setUser(currentUser)

      // Load user's videos and stats
      const [videosResult, statsResult] = await Promise.all([
        getUserVideos(currentUser.id),
        getUserStats(currentUser.id)
      ])

      if (videosResult.error) {
        console.error('Error loading videos:', videosResult.error)
      } else {
        setVideos(videosResult.videos)
      }

      if (statsResult.error) {
        console.error('Error loading stats:', statsResult.error)
      } else {
        setStats(statsResult.stats)
      }
    } catch (error) {
      console.error('Error loading dashboard:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleVideoAdded = () => {
    setShowAddModal(false)
    loadDashboardData() // Refresh data
  }

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">
              Welcome back, {user?.name}!
            </h1>
            <p className="text-gray-400 mt-1">
              Here's what's happening with your videos
            </p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 flex items-center space-x-2"
          >
            <Plus className="w-5 h-5" />
            <span>Add Video</span>
          </button>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Total Videos"
              value={stats.total_videos}
              icon={<Video className="w-6 h-6" />}
              color="blue"
            />
            <StatsCard
              title="Total Views"
              value={stats.total_views}
              icon={<Eye className="w-6 h-6" />}
              color="green"
            />
            <StatsCard
              title="Total Leads"
              value={stats.total_leads}
              icon={<Users className="w-6 h-6" />}
              color="purple"
            />
            <StatsCard
              title="Avg. Completion"
              value={`${stats.avg_completion}%`}
              icon={<TrendingUp className="w-6 h-6" />}
              color="orange"
            />
          </div>
        )}

        {/* Videos Section */}
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-white">Your Videos</h2>
            {videos.length > 0 && (
              <div className="text-gray-400 text-sm">
                {videos.length} video{videos.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>

          {videos.length === 0 ? (
            /* Empty State */
            <div className="bg-slate-800 rounded-lg p-12 text-center">
              <Video className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                No videos yet
              </h3>
              <p className="text-gray-400 mb-6 max-w-md mx-auto">
                Add your first video to start tracking analytics and generating leads. 
                We support YouTube, Vimeo, and Wistia.
              </p>
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 flex items-center space-x-2 mx-auto"
              >
                <Plus className="w-5 h-5" />
                <span>Add Your First Video</span>
              </button>
            </div>
          ) : (
            /* Videos Grid */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {videos.map((video) => (
                <VideoCard
                  key={video.id}
                  video={video}
                  onUpdate={loadDashboardData}
                />
              ))}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        {videos.length > 0 && (
          <div className="bg-slate-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-slate-700 hover:bg-slate-600 text-white p-4 rounded-lg transition-colors flex items-center space-x-3"
              >
                <Plus className="w-5 h-5 text-purple-400" />
                <span>Add Another Video</span>
              </button>
              <button
                onClick={() => router.push('/dashboard/analytics')}
                className="bg-slate-700 hover:bg-slate-600 text-white p-4 rounded-lg transition-colors flex items-center space-x-3"
              >
                <BarChart3 className="w-5 h-5 text-green-400" />
                <span>View Analytics</span>
              </button>
              <button
                onClick={() => router.push('/dashboard/settings')}
                className="bg-slate-700 hover:bg-slate-600 text-white p-4 rounded-lg transition-colors flex items-center space-x-3"
              >
                <Users className="w-5 h-5 text-blue-400" />
                <span>Account Settings</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Add Video Modal */}
      <AddVideoModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onVideoAdded={handleVideoAdded}
        userId={user?.id || ''}
      />
    </DashboardLayout>
  )
}
