'use client'

import { useState } from 'react'
import { Play, MoreVertical, Eye, Users, TrendingUp, ExternalLink, Copy, Trash2 } from 'lucide-react'

interface Video {
  id: string
  title: string
  platform: string
  video_id: string
  duration?: number
  thumbnail_url?: string
  is_active: boolean
  created_at: string
}

interface VideoCardProps {
  video: Video
  onUpdate: () => void
}

export default function VideoCard({ video, onUpdate }: VideoCardProps) {
  const [showMenu, setShowMenu] = useState(false)
  const [stats, setStats] = useState({
    views: 0,
    leads: 0,
    completion: 0
  })

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const copyEmbedCode = () => {
    const embedCode = `<iframe src="${window.location.origin}/embed/${video.id}" width="640" height="360" frameborder="0" allowfullscreen></iframe>`
    navigator.clipboard.writeText(embedCode)
    // You could add a toast notification here
  }

  const platformColors = {
    youtube: 'bg-red-600',
    vimeo: 'bg-blue-600',
    wistia: 'bg-green-600'
  }

  return (
    <div className="bg-slate-800 rounded-lg overflow-hidden border border-slate-700 hover:border-slate-600 transition-colors">
      {/* Video Thumbnail */}
      <div className="relative aspect-video bg-slate-700">
        {video.thumbnail_url ? (
          <img 
            src={video.thumbnail_url}
            alt={video.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Play className="w-12 h-12 text-gray-400" />
          </div>
        )}
        
        {/* Platform Badge */}
        <div className={`absolute top-2 left-2 ${platformColors[video.platform as keyof typeof platformColors] || 'bg-gray-600'} text-white text-xs px-2 py-1 rounded`}>
          {video.platform.toUpperCase()}
        </div>
        
        {/* Duration */}
        {video.duration && (
          <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
            {formatDuration(video.duration)}
          </div>
        )}
        
        {/* Status */}
        <div className={`absolute top-2 right-2 w-3 h-3 rounded-full ${video.is_active ? 'bg-green-400' : 'bg-red-400'}`} />
      </div>

      {/* Video Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-white font-medium text-sm line-clamp-2 flex-1">
            {video.title}
          </h3>
          <div className="relative ml-2">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="text-gray-400 hover:text-white p-1"
            >
              <MoreVertical className="w-4 h-4" />
            </button>
            
            {showMenu && (
              <div className="absolute right-0 top-8 bg-slate-700 border border-slate-600 rounded-lg shadow-lg z-10 min-w-[150px]">
                <button
                  onClick={copyEmbedCode}
                  className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-300 hover:bg-slate-600 hover:text-white"
                >
                  <Copy className="w-4 h-4" />
                  <span>Copy Embed</span>
                </button>
                <button
                  onClick={() => window.open(`/embed/${video.id}`, '_blank')}
                  className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-300 hover:bg-slate-600 hover:text-white"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>Preview</span>
                </button>
                <button
                  onClick={() => {/* Add delete functionality */}}
                  className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-400 hover:bg-slate-600 hover:text-red-300"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Delete</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-3 mb-3">
          <div className="text-center">
            <div className="flex items-center justify-center text-blue-400 mb-1">
              <Eye className="w-4 h-4" />
            </div>
            <div className="text-white text-sm font-medium">{stats.views}</div>
            <div className="text-gray-400 text-xs">Views</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center text-green-400 mb-1">
              <Users className="w-4 h-4" />
            </div>
            <div className="text-white text-sm font-medium">{stats.leads}</div>
            <div className="text-gray-400 text-xs">Leads</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center text-purple-400 mb-1">
              <TrendingUp className="w-4 h-4" />
            </div>
            <div className="text-white text-sm font-medium">{stats.completion}%</div>
            <div className="text-gray-400 text-xs">Completion</div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-gray-400 text-xs">
          Created {formatDate(video.created_at)}
        </div>
      </div>
    </div>
  )
}
