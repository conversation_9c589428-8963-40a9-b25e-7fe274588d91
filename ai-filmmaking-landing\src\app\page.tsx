'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Play, Star, CheckCircle, Users, Clock, Award, BarChart3, Video, Zap, Globe, Shield, Rocket } from 'lucide-react'

export default function Home() {
  const features = [
    {
      icon: <Video className="w-8 h-8" />,
      title: "Multi-Platform Support",
      description: "Works with YouTube, Vimeo, and Wistia videos seamlessly"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Real-Time Analytics",
      description: "Track viewer engagement, completion rates, and lead generation"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Lead Capture",
      description: "Convert video viewers into leads with customizable forms"
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Embeddable Anywhere",
      description: "Embed on any website with our universal widget code"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Secure & Reliable",
      description: "Enterprise-grade security with 99.9% uptime guarantee"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Lightning Fast",
      description: "Optimized for speed with global CDN and instant loading"
    }
  ]

  const benefits = [
    "Multi-tenant SaaS platform with user management",
    "Support for YouTube, Vimeo, and Wistia videos",
    "Real-time video engagement tracking",
    "Automatic lead capture and form generation",
    "Embeddable widgets for any website",
    "Advanced analytics dashboard with insights",
    "Cross-domain tracking and performance metrics",
    "White-label options for enterprise clients",
    "API access for custom integrations",
    "Scalable infrastructure for thousands of users"
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="text-2xl font-bold text-white">
            VideoAnalytics
          </div>
          <div className="flex items-center space-x-4">
            <Link
              href="/login"
              className="text-white hover:text-purple-300 transition-colors"
            >
              Sign In
            </Link>
            <Link
              href="/signup"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300"
            >
              Get Started Free
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative px-4 py-12 lg:py-20">
        <div className="max-w-7xl mx-auto text-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-current" />
                  ))}
                </div>
                <span className="text-sm text-gray-300">4.9/5 (1,247 users)</span>
              </div>

              <h1 className="text-4xl lg:text-7xl font-bold leading-tight text-white">
                Transform Videos Into
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                  {" "}Powerful Analytics
                </span>
              </h1>

              <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
                The complete video analytics SaaS platform. Support for YouTube, Vimeo, and Wistia.
                Generate embeddable widgets, capture leads, and track detailed viewer engagement in real-time.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/signup"
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105"
              >
                Start Free Trial
              </Link>
              <Link
                href="/login"
                className="border border-white/20 hover:bg-white/10 text-white font-medium py-4 px-8 rounded-full text-lg transition-all duration-300"
              >
                Watch Demo
              </Link>
            </div>

            <div className="text-center text-gray-400 text-sm">
              <p>✓ Free forever plan • ✓ No credit card required • ✓ Setup in 2 minutes</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="px-4 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Complete Video Analytics SaaS Platform
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Everything you need to turn videos into lead generation machines with detailed tracking and insights
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white/5 backdrop-blur-sm rounded-xl p-8 border border-white/10 hover:border-purple-500/50 transition-all duration-300 group">
                <div className="text-purple-400 mb-4 group-hover:scale-110 transition-transform">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="px-4 py-16 bg-slate-800/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Get started in minutes with our simple 3-step process
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Add Your Video</h3>
              <p className="text-gray-300">
                Paste any YouTube, Vimeo, or Wistia URL. We'll automatically detect the platform and extract metadata.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Get Embed Code</h3>
              <p className="text-gray-300">
                Copy the generated embed code and paste it anywhere on your website or landing page.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Track & Analyze</h3>
              <p className="text-gray-300">
                Watch real-time analytics as viewers engage with your content and convert into leads.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="px-4 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Why Choose Our Video Analytics Platform?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              The most comprehensive video analytics SaaS tool for marketers, creators, and businesses
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start space-x-4 bg-white/5 backdrop-blur-sm rounded-lg p-6">
                <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0 mt-1" />
                <span className="text-white text-lg">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="px-4 py-16 bg-slate-800/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Trusted by Thousands of Creators
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              See what our users are saying about VideoAnalytics
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <div className="flex text-yellow-400 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-current" />
                ))}
              </div>
              <p className="text-gray-300 mb-4">
                "VideoAnalytics transformed how we track our marketing videos. The lead capture feature alone increased our conversions by 300%."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white text-sm font-bold">SM</span>
                </div>
                <div>
                  <div className="text-white font-medium">Sarah Mitchell</div>
                  <div className="text-gray-400 text-sm">Marketing Director, TechCorp</div>
                </div>
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <div className="flex text-yellow-400 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-current" />
                ))}
              </div>
              <p className="text-gray-300 mb-4">
                "The multi-platform support is incredible. We use YouTube, Vimeo, and Wistia, and VideoAnalytics tracks them all seamlessly."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white text-sm font-bold">JD</span>
                </div>
                <div>
                  <div className="text-white font-medium">James Davis</div>
                  <div className="text-gray-400 text-sm">Content Creator</div>
                </div>
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <div className="flex text-yellow-400 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-current" />
                ))}
              </div>
              <p className="text-gray-300 mb-4">
                "Finally, a video analytics tool that actually works across all our landing pages. The embed system is brilliant!"
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white text-sm font-bold">AL</span>
                </div>
                <div>
                  <div className="text-white font-medium">Anna Lopez</div>
                  <div className="text-gray-400 text-sm">Growth Manager, StartupXYZ</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 lg:p-12 border border-white/10">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Choose the perfect plan for your video analytics needs. Start free, upgrade anytime.
            </p>

            <div className="grid md:grid-cols-3 gap-6 mb-8">
              {/* Free Plan */}
              <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                <h3 className="text-xl font-bold text-white mb-2">Free</h3>
                <div className="text-3xl font-bold text-white mb-4">$0<span className="text-sm text-gray-400">/month</span></div>
                <ul className="text-gray-300 text-sm space-y-2 mb-6">
                  <li>✓ 1 video</li>
                  <li>✓ Unlimited views</li>
                  <li>✓ Basic analytics</li>
                  <li>✓ Lead capture</li>
                </ul>
                <Link href="/signup" className="block bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                  Get Started
                </Link>
              </div>

              {/* Pro Plan */}
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 border-2 border-purple-400 relative">
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold">
                  POPULAR
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Pro</h3>
                <div className="text-3xl font-bold text-white mb-4">$29<span className="text-sm text-gray-200">/month</span></div>
                <ul className="text-white text-sm space-y-2 mb-6">
                  <li>✓ Unlimited videos</li>
                  <li>✓ Advanced analytics</li>
                  <li>✓ Custom branding</li>
                  <li>✓ A/B testing</li>
                  <li>✓ Priority support</li>
                </ul>
                <Link href="/signup" className="block bg-white text-purple-600 font-bold py-2 px-4 rounded-lg transition-colors hover:bg-gray-100">
                  Start Free Trial
                </Link>
              </div>

              {/* Enterprise Plan */}
              <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                <h3 className="text-xl font-bold text-white mb-2">Enterprise</h3>
                <div className="text-3xl font-bold text-white mb-4">Custom</div>
                <ul className="text-gray-300 text-sm space-y-2 mb-6">
                  <li>✓ Everything in Pro</li>
                  <li>✓ White-label solution</li>
                  <li>✓ API access</li>
                  <li>✓ Dedicated support</li>
                  <li>✓ Custom integrations</li>
                </ul>
                <Link href="/contact" className="block bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                  Contact Sales
                </Link>
              </div>
            </div>

            <div className="text-center text-gray-400 text-sm">
              <p>✓ 14-day free trial • ✓ No setup fees • ✓ Cancel anytime</p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="px-4 py-16 bg-gradient-to-r from-purple-600/20 to-pink-600/20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Video Strategy?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of marketers and creators who are already using VideoAnalytics to boost their conversions
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Link
              href="/signup"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105"
            >
              Start Your Free Trial
            </Link>
            <Link
              href="/login"
              className="border border-white/20 hover:bg-white/10 text-white font-medium py-4 px-8 rounded-full text-lg transition-all duration-300"
            >
              View Live Demo
            </Link>
          </div>

          <div className="text-center text-gray-400 text-sm">
            <p>🚀 Setup in 2 minutes • 📊 Real-time analytics • 🔒 Enterprise security</p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-700 px-4 py-12">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold text-white mb-4">VideoAnalytics</h3>
              <p className="text-gray-400 text-sm">
                The complete video analytics SaaS platform for marketers, creators, and businesses.
              </p>
            </div>

            <div>
              <h4 className="text-white font-medium mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><Link href="/features" className="hover:text-white transition-colors">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/integrations" className="hover:text-white transition-colors">Integrations</Link></li>
                <li><Link href="/api" className="hover:text-white transition-colors">API</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-white font-medium mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/careers" className="hover:text-white transition-colors">Careers</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400 text-sm">
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/docs" className="hover:text-white transition-colors">Documentation</Link></li>
                <li><Link href="/status" className="hover:text-white transition-colors">Status</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-700 mt-8 pt-8 text-center text-gray-400 text-sm">
            <p>&copy; 2024 VideoAnalytics. All rights reserved. Built with ❤️ for creators and marketers.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
