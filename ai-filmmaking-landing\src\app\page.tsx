'use client'

import { useState } from 'react'
import { Play, Star, CheckCircle, Users, Clock, Award } from 'lucide-react'
import VideoModal from '@/components/VideoModal'

export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const features = [
    {
      icon: <Award className="w-6 h-6" />,
      title: "Professional AI Tools",
      description: "Master cutting-edge AI filmmaking tools used by industry professionals"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Expert Instruction",
      description: "Learn from award-winning filmmakers and AI technology experts"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "11+ Hours Content",
      description: "Comprehensive course with hands-on projects and real-world examples"
    }
  ]

  const benefits = [
    "Create stunning AI-generated visuals and effects",
    "Streamline your post-production workflow",
    "Generate scripts and storyboards with AI",
    "Master AI voice synthesis and audio enhancement",
    "Build a professional portfolio of AI-enhanced films",
    "Access to exclusive AI filmmaking tools and resources"
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="text-2xl font-bold text-white">
            AI Filmmaking Pro
          </div>
          <div className="text-white">
            <span className="text-sm opacity-75">Premium Course</span>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative px-4 py-12 lg:py-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <div className="text-white space-y-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-current" />
                    ))}
                  </div>
                  <span className="text-sm text-gray-300">4.9/5 (2,847 reviews)</span>
                </div>

                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Master AI Filmmaking in
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                    {" "}30 Days
                  </span>
                </h1>

                <p className="text-xl text-gray-300 leading-relaxed">
                  Transform your filmmaking with cutting-edge AI tools. Create Hollywood-quality
                  content faster than ever before with our comprehensive course.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg p-6 flex-1">
                  <div className="text-center">
                    <div className="text-3xl font-bold">$199</div>
                    <div className="text-sm opacity-90">One-time payment</div>
                    <div className="text-xs mt-1 line-through opacity-60">$499 regular price</div>
                  </div>
                </div>
                <div className="text-center flex-1">
                  <div className="text-2xl font-semibold text-green-400">60% OFF</div>
                  <div className="text-sm text-gray-300">Limited time offer</div>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div className="text-purple-400 mb-2">{feature.icon}</div>
                    <h3 className="font-semibold text-sm mb-1">{feature.title}</h3>
                    <p className="text-xs text-gray-300">{feature.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Column - Video */}
            <div className="relative">
              <div className="relative bg-black rounded-2xl overflow-hidden shadow-2xl">
                <div className="aspect-video relative">
                  <img
                    src="https://img.youtube.com/vi/s-hLAqtNJvg/maxresdefault.jpg"
                    alt="AI Filmmaking Course Preview"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                    <button
                      onClick={() => setIsModalOpen(true)}
                      className="bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-all duration-300 rounded-full p-6 group"
                    >
                      <Play className="w-12 h-12 text-white ml-1 group-hover:scale-110 transition-transform" />
                    </button>
                  </div>
                  <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-1">
                    <span className="text-white text-sm font-medium">11:47</span>
                  </div>
                </div>
              </div>
              <div className="absolute -top-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-2 rounded-full text-sm font-bold">
                FREE PREVIEW
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What You'll Learn Section */}
      <section className="px-4 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              What You'll Master
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Transform your creative vision into reality with industry-leading AI tools and techniques
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start space-x-4 bg-white/5 backdrop-blur-sm rounded-lg p-6">
                <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0 mt-1" />
                <span className="text-white text-lg">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 lg:p-12 border border-white/10">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Ready to Transform Your Filmmaking?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of creators who are already using AI to revolutionize their content
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105">
                Get Instant Access - $199
              </button>
              <div className="text-gray-300 text-sm">
                <div>✓ 30-day money-back guarantee</div>
                <div>✓ Lifetime access to updates</div>
              </div>
            </div>

            <div className="text-center text-gray-400 text-sm">
              <p>Secure payment • Instant access • No monthly fees</p>
            </div>
          </div>
        </div>
      </section>

      {/* Video Modal */}
      <VideoModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  )
}
