-- VideoAnalytics SaaS Platform Database Migration
-- This script migrates from single-video to multi-tenant SaaS structure

-- First, let's check if we need to preserve existing data
-- If you have important data in video_viewers and video_analytics, 
-- this script will preserve it by creating a default user

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (SaaS customers) if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  name VARCHAR(255) NOT NULL,
  password_hash VARCHAR(255),
  plan VARCHAR(50) DEFAULT 'free', -- free, pro, enterprise
  stripe_customer_id VARCHAR(255),
  subscription_status VARCHAR(50) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create videos table (each user can have multiple videos) if it doesn't exist
CREATE TABLE IF NOT EXISTS videos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  video_url TEXT NOT NULL,
  video_id VARCHAR(255) NOT NULL, -- extracted from URL
  platform VARCHAR(50) NOT NULL, -- youtube, vimeo, wistia
  duration INTEGER, -- in seconds
  thumbnail_url TEXT,
  embed_code TEXT, -- generated embed code
  is_active BOOLEAN DEFAULT true,
  custom_settings JSONB DEFAULT '{}', -- player customization
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update video_viewers table to reference videos instead of being standalone
-- First, add video_id column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_viewers' AND column_name = 'video_id') THEN
    ALTER TABLE video_viewers ADD COLUMN video_id UUID REFERENCES videos(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Add additional columns to video_viewers if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_viewers' AND column_name = 'ip_address') THEN
    ALTER TABLE video_viewers ADD COLUMN ip_address INET;
  END IF;
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_viewers' AND column_name = 'user_agent') THEN
    ALTER TABLE video_viewers ADD COLUMN user_agent TEXT;
  END IF;
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_viewers' AND column_name = 'referrer') THEN
    ALTER TABLE video_viewers ADD COLUMN referrer TEXT;
  END IF;
END $$;

-- Update video_analytics table structure
-- Add new columns if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_analytics' AND column_name = 'max_position_reached') THEN
    ALTER TABLE video_analytics ADD COLUMN max_position_reached INTEGER DEFAULT 0;
  END IF;
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'video_analytics' AND column_name = 'segments_watched') THEN
    ALTER TABLE video_analytics ADD COLUMN segments_watched JSONB DEFAULT '[]';
  END IF;
END $$;

-- Create lead_forms table if it doesn't exist
CREATE TABLE IF NOT EXISTS lead_forms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  title VARCHAR(255) DEFAULT 'Watch Video',
  description TEXT DEFAULT 'Enter your details to watch the video',
  fields JSONB DEFAULT '[{"name":"name","label":"Full Name","required":true},{"name":"email","label":"Email Address","required":true}]',
  styling JSONB DEFAULT '{}', -- custom CSS/styling
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create embed_analytics table if it doesn't exist
CREATE TABLE IF NOT EXISTS embed_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  domain VARCHAR(255), -- where the embed is hosted
  page_url TEXT,
  views INTEGER DEFAULT 0,
  conversions INTEGER DEFAULT 0, -- form submissions
  date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(video_id, domain, date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_platform ON videos(platform);
CREATE INDEX IF NOT EXISTS idx_video_viewers_video_id ON video_viewers(video_id);
CREATE INDEX IF NOT EXISTS idx_video_viewers_email ON video_viewers(email);
CREATE INDEX IF NOT EXISTS idx_video_analytics_video_id ON video_analytics(video_id);
CREATE INDEX IF NOT EXISTS idx_video_analytics_viewer_id ON video_analytics(viewer_id);
CREATE INDEX IF NOT EXISTS idx_lead_forms_video_id ON lead_forms(video_id);
CREATE INDEX IF NOT EXISTS idx_embed_analytics_video_id ON embed_analytics(video_id);
CREATE INDEX IF NOT EXISTS idx_embed_analytics_date ON embed_analytics(date);

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_viewers ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE lead_forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE embed_analytics ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Users can view own videos" ON videos;
DROP POLICY IF EXISTS "Users can insert own videos" ON videos;
DROP POLICY IF EXISTS "Users can update own videos" ON videos;
DROP POLICY IF EXISTS "Users can delete own videos" ON videos;
DROP POLICY IF EXISTS "Allow public access to active videos" ON videos;
DROP POLICY IF EXISTS "Allow public insert on video_viewers" ON video_viewers;
DROP POLICY IF EXISTS "Users can view viewers of their videos" ON video_viewers;
DROP POLICY IF EXISTS "Allow public insert on video_analytics" ON video_analytics;
DROP POLICY IF EXISTS "Allow public update on video_analytics" ON video_analytics;
DROP POLICY IF EXISTS "Users can view analytics of their videos" ON video_analytics;
DROP POLICY IF EXISTS "Users can manage lead forms for their videos" ON lead_forms;
DROP POLICY IF EXISTS "Allow public access to lead forms" ON lead_forms;
DROP POLICY IF EXISTS "Users can view embed analytics for their videos" ON embed_analytics;
DROP POLICY IF EXISTS "Allow public insert/update on embed_analytics" ON embed_analytics;
DROP POLICY IF EXISTS "Allow public update on embed_analytics" ON embed_analytics;

-- Create RLS Policies

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid()::text = id::text);

-- Videos table policies
CREATE POLICY "Users can view own videos" ON videos
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own videos" ON videos
  FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own videos" ON videos
  FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete own videos" ON videos
  FOR DELETE USING (auth.uid()::text = user_id::text);

-- Public access for embed functionality
CREATE POLICY "Allow public access to active videos" ON videos
  FOR SELECT USING (is_active = true);

-- Video viewers policies (public insert, user can view)
CREATE POLICY "Allow public insert on video_viewers" ON video_viewers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view viewers of their videos" ON video_viewers
  FOR SELECT USING (
    video_id IN (SELECT id FROM videos WHERE user_id::text = auth.uid()::text)
  );

-- Video analytics policies (public insert/update, user can view)
CREATE POLICY "Allow public insert on video_analytics" ON video_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public update on video_analytics" ON video_analytics
  FOR UPDATE USING (true);

CREATE POLICY "Users can view analytics of their videos" ON video_analytics
  FOR SELECT USING (
    video_id IN (SELECT id FROM videos WHERE user_id::text = auth.uid()::text)
  );

-- Lead forms policies
CREATE POLICY "Users can manage lead forms for their videos" ON lead_forms
  FOR ALL USING (
    video_id IN (SELECT id FROM videos WHERE user_id::text = auth.uid()::text)
  );

CREATE POLICY "Allow public access to lead forms" ON lead_forms
  FOR SELECT USING (true);

-- Embed analytics policies
CREATE POLICY "Users can view embed analytics for their videos" ON embed_analytics
  FOR SELECT USING (
    video_id IN (SELECT id FROM videos WHERE user_id::text = auth.uid()::text)
  );

CREATE POLICY "Allow public insert on embed_analytics" ON embed_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public update on embed_analytics" ON embed_analytics
  FOR UPDATE USING (true);

-- Create a default demo user and video if there's existing data but no users
DO $$
DECLARE
  demo_user_id UUID;
  demo_video_id UUID;
  viewer_count INTEGER;
BEGIN
  -- Check if we have viewers but no users
  SELECT COUNT(*) INTO viewer_count FROM video_viewers;
  
  IF viewer_count > 0 AND (SELECT COUNT(*) FROM users) = 0 THEN
    -- Create demo user
    INSERT INTO users (id, email, name, plan)
    VALUES (gen_random_uuid(), '<EMAIL>', 'Demo User', 'pro')
    RETURNING id INTO demo_user_id;
    
    -- Create demo video
    INSERT INTO videos (id, user_id, title, video_url, video_id, platform, duration, is_active)
    VALUES (
      gen_random_uuid(),
      demo_user_id,
      'Demo Video',
      'https://www.youtube.com/watch?v=s-hLAqtNJvg',
      's-hLAqtNJvg',
      'youtube',
      707,
      true
    ) RETURNING id INTO demo_video_id;
    
    -- Update existing video_viewers to reference the demo video
    UPDATE video_viewers SET video_id = demo_video_id WHERE video_id IS NULL;
    
    -- Create default lead form for demo video
    INSERT INTO lead_forms (video_id, title, description)
    VALUES (demo_video_id, 'Watch Demo Video', 'Enter your details to watch our demo video');
    
    RAISE NOTICE 'Created demo user and migrated existing data';
  END IF;
END $$;

-- Create helper function for user stats
CREATE OR REPLACE FUNCTION get_user_video_stats(user_uuid UUID)
RETURNS TABLE (
  total_videos INTEGER,
  total_views INTEGER,
  total_leads INTEGER,
  avg_completion DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(DISTINCT v.id)::INTEGER as total_videos,
    COUNT(DISTINCT vv.id)::INTEGER as total_views,
    COUNT(DISTINCT vv.id)::INTEGER as total_leads,
    COALESCE(AVG(va.completion_percentage), 0)::DECIMAL as avg_completion
  FROM videos v
  LEFT JOIN video_viewers vv ON v.id = vv.video_id
  LEFT JOIN video_analytics va ON v.id = va.video_id
  WHERE v.user_id = user_uuid AND v.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Success message
SELECT 'SaaS Database Migration Completed Successfully! 🎉' as message;
