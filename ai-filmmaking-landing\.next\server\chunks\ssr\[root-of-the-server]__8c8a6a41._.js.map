{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/V2/ai-filmmaking-landing/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Play, Star, CheckCircle, Users, Clock, Award, BarChart3, Video, Zap, Globe, Shield, Rocket } from 'lucide-react'\n\nexport default function Home() {\n  const features = [\n    {\n      icon: <Video className=\"w-8 h-8\" />,\n      title: \"Multi-Platform Support\",\n      description: \"Works with YouTube, Vimeo, and Wistia videos seamlessly\"\n    },\n    {\n      icon: <BarChart3 className=\"w-8 h-8\" />,\n      title: \"Real-Time Analytics\",\n      description: \"Track viewer engagement, completion rates, and lead generation\"\n    },\n    {\n      icon: <Users className=\"w-8 h-8\" />,\n      title: \"Lead Capture\",\n      description: \"Convert video viewers into leads with customizable forms\"\n    },\n    {\n      icon: <Globe className=\"w-8 h-8\" />,\n      title: \"Embeddable Anywhere\",\n      description: \"Embed on any website with our universal widget code\"\n    },\n    {\n      icon: <Shield className=\"w-8 h-8\" />,\n      title: \"Secure & Reliable\",\n      description: \"Enterprise-grade security with 99.9% uptime guarantee\"\n    },\n    {\n      icon: <Zap className=\"w-8 h-8\" />,\n      title: \"Lightning Fast\",\n      description: \"Optimized for speed with global CDN and instant loading\"\n    }\n  ]\n\n  const benefits = [\n    \"Multi-tenant SaaS platform with user management\",\n    \"Support for YouTube, Vimeo, and Wistia videos\",\n    \"Real-time video engagement tracking\",\n    \"Automatic lead capture and form generation\",\n    \"Embeddable widgets for any website\",\n    \"Advanced analytics dashboard with insights\",\n    \"Cross-domain tracking and performance metrics\",\n    \"White-label options for enterprise clients\",\n    \"API access for custom integrations\",\n    \"Scalable infrastructure for thousands of users\"\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Header */}\n      <header className=\"relative z-10 px-4 py-6\">\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div className=\"text-2xl font-bold text-white\">\n            VideoAnalytics\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/login\"\n              className=\"text-white hover:text-purple-300 transition-colors\"\n            >\n              Sign In\n            </Link>\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300\"\n            >\n              Get Started Free\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"relative px-4 py-12 lg:py-20\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <div className=\"space-y-8\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"flex text-yellow-400\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"w-5 h-5 fill-current\" />\n                  ))}\n                </div>\n                <span className=\"text-sm text-gray-300\">4.9/5 (1,247 users)</span>\n              </div>\n\n              <h1 className=\"text-4xl lg:text-7xl font-bold leading-tight text-white\">\n                Transform Videos Into\n                <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400\">\n                  {\" \"}Powerful Analytics\n                </span>\n              </h1>\n\n              <p className=\"text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto\">\n                The complete video analytics SaaS platform. Support for YouTube, Vimeo, and Wistia.\n                Generate embeddable widgets, capture leads, and track detailed viewer engagement in real-time.\n              </p>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <Link\n                href=\"/signup\"\n                className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105\"\n              >\n                Start Free Trial\n              </Link>\n              <Link\n                href=\"/login\"\n                className=\"border border-white/20 hover:bg-white/10 text-white font-medium py-4 px-8 rounded-full text-lg transition-all duration-300\"\n              >\n                Watch Demo\n              </Link>\n            </div>\n\n            <div className=\"text-center text-gray-400 text-sm\">\n              <p>✓ Free forever plan • ✓ No credit card required • ✓ Setup in 2 minutes</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"px-4 py-16\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              Complete Video Analytics SaaS Platform\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              Everything you need to turn videos into lead generation machines with detailed tracking and insights\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"bg-white/5 backdrop-blur-sm rounded-xl p-8 border border-white/10 hover:border-purple-500/50 transition-all duration-300 group\">\n                <div className=\"text-purple-400 mb-4 group-hover:scale-110 transition-transform\">\n                  {feature.icon}\n                </div>\n                <h3 className=\"text-xl font-semibold text-white mb-3\">{feature.title}</h3>\n                <p className=\"text-gray-300 leading-relaxed\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works Section */}\n      <section className=\"px-4 py-16 bg-slate-800/50\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              How It Works\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              Get started in minutes with our simple 3-step process\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-white text-2xl font-bold\">1</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-white mb-3\">Add Your Video</h3>\n              <p className=\"text-gray-300\">\n                Paste any YouTube, Vimeo, or Wistia URL. We'll automatically detect the platform and extract metadata.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-white text-2xl font-bold\">2</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-white mb-3\">Get Embed Code</h3>\n              <p className=\"text-gray-300\">\n                Copy the generated embed code and paste it anywhere on your website or landing page.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-white text-2xl font-bold\">3</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-white mb-3\">Track & Analyze</h3>\n              <p className=\"text-gray-300\">\n                Watch real-time analytics as viewers engage with your content and convert into leads.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Benefits Section */}\n      <section className=\"px-4 py-16\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              Why Choose Our Video Analytics Platform?\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              The most comprehensive video analytics SaaS tool for marketers, creators, and businesses\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {benefits.map((benefit, index) => (\n              <div key={index} className=\"flex items-start space-x-4 bg-white/5 backdrop-blur-sm rounded-lg p-6\">\n                <CheckCircle className=\"w-6 h-6 text-green-400 flex-shrink-0 mt-1\" />\n                <span className=\"text-white text-lg\">{benefit}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section */}\n      <section className=\"px-4 py-16 bg-slate-800/30\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              Trusted by Thousands of Creators\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              See what our users are saying about VideoAnalytics\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10\">\n              <div className=\"flex text-yellow-400 mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-300 mb-4\">\n                \"VideoAnalytics transformed how we track our marketing videos. The lead capture feature alone increased our conversions by 300%.\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-3\">\n                  <span className=\"text-white text-sm font-bold\">SM</span>\n                </div>\n                <div>\n                  <div className=\"text-white font-medium\">Sarah Mitchell</div>\n                  <div className=\"text-gray-400 text-sm\">Marketing Director, TechCorp</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10\">\n              <div className=\"flex text-yellow-400 mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-300 mb-4\">\n                \"The multi-platform support is incredible. We use YouTube, Vimeo, and Wistia, and VideoAnalytics tracks them all seamlessly.\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-3\">\n                  <span className=\"text-white text-sm font-bold\">JD</span>\n                </div>\n                <div>\n                  <div className=\"text-white font-medium\">James Davis</div>\n                  <div className=\"text-gray-400 text-sm\">Content Creator</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10\">\n              <div className=\"flex text-yellow-400 mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-300 mb-4\">\n                \"Finally, a video analytics tool that actually works across all our landing pages. The embed system is brilliant!\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-3\">\n                  <span className=\"text-white text-sm font-bold\">AL</span>\n                </div>\n                <div>\n                  <div className=\"text-white font-medium\">Anna Lopez</div>\n                  <div className=\"text-gray-400 text-sm\">Growth Manager, StartupXYZ</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Pricing Section */}\n      <section className=\"px-4 py-16\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <div className=\"bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-2xl p-8 lg:p-12 border border-white/10\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              Simple, Transparent Pricing\n            </h2>\n            <p className=\"text-xl text-gray-300 mb-8\">\n              Choose the perfect plan for your video analytics needs. Start free, upgrade anytime.\n            </p>\n\n            <div className=\"grid md:grid-cols-3 gap-6 mb-8\">\n              {/* Free Plan */}\n              <div className=\"bg-white/10 rounded-xl p-6 border border-white/20\">\n                <h3 className=\"text-xl font-bold text-white mb-2\">Free</h3>\n                <div className=\"text-3xl font-bold text-white mb-4\">$0<span className=\"text-sm text-gray-400\">/month</span></div>\n                <ul className=\"text-gray-300 text-sm space-y-2 mb-6\">\n                  <li>✓ 1 video</li>\n                  <li>✓ Unlimited views</li>\n                  <li>✓ Basic analytics</li>\n                  <li>✓ Lead capture</li>\n                </ul>\n                <Link href=\"/signup\" className=\"block bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\">\n                  Get Started\n                </Link>\n              </div>\n\n              {/* Pro Plan */}\n              <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 border-2 border-purple-400 relative\">\n                <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold\">\n                  POPULAR\n                </div>\n                <h3 className=\"text-xl font-bold text-white mb-2\">Pro</h3>\n                <div className=\"text-3xl font-bold text-white mb-4\">$29<span className=\"text-sm text-gray-200\">/month</span></div>\n                <ul className=\"text-white text-sm space-y-2 mb-6\">\n                  <li>✓ Unlimited videos</li>\n                  <li>✓ Advanced analytics</li>\n                  <li>✓ Custom branding</li>\n                  <li>✓ A/B testing</li>\n                  <li>✓ Priority support</li>\n                </ul>\n                <Link href=\"/signup\" className=\"block bg-white text-purple-600 font-bold py-2 px-4 rounded-lg transition-colors hover:bg-gray-100\">\n                  Start Free Trial\n                </Link>\n              </div>\n\n              {/* Enterprise Plan */}\n              <div className=\"bg-white/10 rounded-xl p-6 border border-white/20\">\n                <h3 className=\"text-xl font-bold text-white mb-2\">Enterprise</h3>\n                <div className=\"text-3xl font-bold text-white mb-4\">Custom</div>\n                <ul className=\"text-gray-300 text-sm space-y-2 mb-6\">\n                  <li>✓ Everything in Pro</li>\n                  <li>✓ White-label solution</li>\n                  <li>✓ API access</li>\n                  <li>✓ Dedicated support</li>\n                  <li>✓ Custom integrations</li>\n                </ul>\n                <Link href=\"/contact\" className=\"block bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\">\n                  Contact Sales\n                </Link>\n              </div>\n            </div>\n\n            <div className=\"text-center text-gray-400 text-sm\">\n              <p>✓ 14-day free trial • ✓ No setup fees • ✓ Cancel anytime</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Final CTA Section */}\n      <section className=\"px-4 py-16 bg-gradient-to-r from-purple-600/20 to-pink-600/20\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n            Ready to Transform Your Video Strategy?\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Join thousands of marketers and creators who are already using VideoAnalytics to boost their conversions\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Free Trial\n            </Link>\n            <Link\n              href=\"/login\"\n              className=\"border border-white/20 hover:bg-white/10 text-white font-medium py-4 px-8 rounded-full text-lg transition-all duration-300\"\n            >\n              View Live Demo\n            </Link>\n          </div>\n\n          <div className=\"text-center text-gray-400 text-sm\">\n            <p>🚀 Setup in 2 minutes • 📊 Real-time analytics • 🔒 Enterprise security</p>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-slate-900 border-t border-slate-700 px-4 py-12\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <h3 className=\"text-xl font-bold text-white mb-4\">VideoAnalytics</h3>\n              <p className=\"text-gray-400 text-sm\">\n                The complete video analytics SaaS platform for marketers, creators, and businesses.\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"text-white font-medium mb-4\">Product</h4>\n              <ul className=\"space-y-2 text-gray-400 text-sm\">\n                <li><Link href=\"/features\" className=\"hover:text-white transition-colors\">Features</Link></li>\n                <li><Link href=\"/pricing\" className=\"hover:text-white transition-colors\">Pricing</Link></li>\n                <li><Link href=\"/integrations\" className=\"hover:text-white transition-colors\">Integrations</Link></li>\n                <li><Link href=\"/api\" className=\"hover:text-white transition-colors\">API</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-white font-medium mb-4\">Company</h4>\n              <ul className=\"space-y-2 text-gray-400 text-sm\">\n                <li><Link href=\"/about\" className=\"hover:text-white transition-colors\">About</Link></li>\n                <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n                <li><Link href=\"/careers\" className=\"hover:text-white transition-colors\">Careers</Link></li>\n                <li><Link href=\"/contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-white font-medium mb-4\">Support</h4>\n              <ul className=\"space-y-2 text-gray-400 text-sm\">\n                <li><Link href=\"/help\" className=\"hover:text-white transition-colors\">Help Center</Link></li>\n                <li><Link href=\"/docs\" className=\"hover:text-white transition-colors\">Documentation</Link></li>\n                <li><Link href=\"/status\" className=\"hover:text-white transition-colors\">Status</Link></li>\n                <li><Link href=\"/privacy\" className=\"hover:text-white transition-colors\">Privacy</Link></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-slate-700 mt-8 pt-8 text-center text-gray-400 text-sm\">\n            <p>&copy; 2024 VideoAnalytics. All rights reserved. Built with ❤️ for creators and marketers.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAMe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgC;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGf,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAG1C,8OAAC;wCAAG,WAAU;;4CAA0D;0DAEtE,8OAAC;gDAAK,WAAU;;oDACb;oDAAI;;;;;;;;;;;;;kDAIT,8OAAC;wCAAE,WAAU;kDAA0D;;;;;;;;;;;;0CAMzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI;;;;;;sDAEf,8OAAC;4CAAG,WAAU;sDAAyC,QAAQ,KAAK;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAiC,QAAQ,WAAW;;;;;;;mCALzD;;;;;;;;;;;;;;;;;;;;;0BAalB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;mCAF9B;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;sEACxC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;sEACxC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;sEACxC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;;oDAAqC;kEAAE,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAC9F,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAEN,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAsG;;;;;;;;;;;;kDAMvI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAwH;;;;;;0DAGvI,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;;oDAAqC;kEAAG,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAC/F,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAEN,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAoG;;;;;;;;;;;;kDAMrI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAEN,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsG;;;;;;;;;;;;;;;;;;0CAM1I,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAMT,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAqC;;;;;;;;;;;8DAC1E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAqC;;;;;;;;;;;8DACzE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAgB,WAAU;kEAAqC;;;;;;;;;;;8DAC9E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAO,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAIzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAqC;;;;;;;;;;;8DACvE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAqC;;;;;;;;;;;8DACtE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAqC;;;;;;;;;;;8DACzE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAqC;;;;;;;;;;;8DACtE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAqC;;;;;;;;;;;8DACtE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAqC;;;;;;;;;;;8DACxE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK/E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}