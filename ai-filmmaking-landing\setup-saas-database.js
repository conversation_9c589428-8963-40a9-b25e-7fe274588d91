const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hzsiyumsfpqofiynsbqx.supabase.co'
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6c2l5dW1zZnBxb2ZpeW5zYnF4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNzAzMCwiZXhwIjoyMDY2NjEzMDMwfQ.I7iuGrqM94GqY5Ya54rlJ1Bo_V2B3LxyM-7yAlN5dcQ'

const supabase = createClient(supabaseUrl, serviceRoleKey)

async function setupSaaSDatabase() {
  console.log('🚀 Setting up SaaS Video Analytics Database...')
  
  try {
    console.log('📋 This will create the complete multi-tenant database schema')
    console.log('⚠️  Note: This will modify your existing database structure')
    console.log('')
    
    // Check if we should proceed
    console.log('🔧 Creating SaaS database tables...')
    
    // Test connection first
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('count(*)')
      .limit(1)
    
    if (testError && testError.code === '42P01') {
      console.log('✅ Ready to create new SaaS schema')
    } else if (!testError) {
      console.log('ℹ️  Users table already exists - checking compatibility...')
    }
    
    console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('🛠️  MANUAL SETUP REQUIRED:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('1. 🌐 Open your Supabase Dashboard SQL Editor:')
    console.log('   https://supabase.com/dashboard/project/hzsiyumsfpqofiynsbqx/sql')
    console.log('')
    console.log('2. 📄 Copy and paste the complete SaaS schema from:')
    console.log('   database-schema-saas.sql')
    console.log('')
    console.log('3. ▶️  Click "Run" to execute the entire schema')
    console.log('')
    console.log('4. 🔄 After running the SQL, restart your development server:')
    console.log('   npm run dev')
    console.log('')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    
    console.log('\n📊 What this will create:')
    console.log('✅ Multi-tenant user system')
    console.log('✅ Video management (YouTube, Vimeo, Wistia)')
    console.log('✅ Advanced analytics tracking')
    console.log('✅ Customizable lead capture forms')
    console.log('✅ Embed analytics and tracking')
    console.log('✅ Row Level Security (RLS) policies')
    console.log('✅ Performance indexes')
    
    console.log('\n🎯 After setup, you can:')
    console.log('• Sign up at: http://localhost:3000/signup')
    console.log('• Access dashboard at: http://localhost:3000/dashboard')
    console.log('• Add videos from YouTube, Vimeo, or Wistia')
    console.log('• Generate embed codes for any website')
    console.log('• Track detailed video analytics')
    
    console.log('\n⏳ Run this command again after executing the SQL to verify setup:')
    console.log('   node setup-saas-database.js')
    
  } catch (error) {
    console.error('❌ Setup check failed:', error.message)
  }
}

async function verifySetup() {
  console.log('🔍 Verifying SaaS database setup...')
  
  try {
    // Check all required tables
    const tables = ['users', 'videos', 'video_viewers', 'video_analytics', 'lead_forms', 'embed_analytics']
    const results = []
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count(*)')
          .limit(1)
        
        if (error) {
          results.push({ table, status: 'missing', error: error.message })
        } else {
          results.push({ table, status: 'exists', count: data?.[0]?.count || 0 })
        }
      } catch (err) {
        results.push({ table, status: 'error', error: err.message })
      }
    }
    
    console.log('\n📋 Database Table Status:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    
    let allTablesExist = true
    for (const result of results) {
      if (result.status === 'exists') {
        console.log(`✅ ${result.table.padEnd(20)} - Ready`)
      } else {
        console.log(`❌ ${result.table.padEnd(20)} - ${result.status}`)
        allTablesExist = false
      }
    }
    
    if (allTablesExist) {
      console.log('\n🎉 SaaS Database Setup Complete!')
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
      console.log('🚀 Your Video Analytics SaaS Platform is ready!')
      console.log('')
      console.log('📱 Access Points:')
      console.log('• Landing: http://localhost:3000')
      console.log('• Sign Up: http://localhost:3000/signup')
      console.log('• Login:   http://localhost:3000/login')
      console.log('• Dashboard: http://localhost:3000/dashboard')
      console.log('')
      console.log('🎬 Features Available:')
      console.log('• Multi-user authentication')
      console.log('• YouTube, Vimeo & Wistia support')
      console.log('• Embeddable video widgets')
      console.log('• Real-time analytics tracking')
      console.log('• Lead capture forms')
      console.log('• Cross-domain embedding')
      console.log('')
      console.log('🔧 Test the platform:')
      console.log('1. Create an account')
      console.log('2. Add a video URL')
      console.log('3. Get embed code')
      console.log('4. Test on any website')
      console.log('5. View analytics dashboard')
      
    } else {
      console.log('\n⚠️  Some tables are missing. Please run the SQL schema first.')
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message)
  }
}

// Check if we're verifying or setting up
const args = process.argv.slice(2)
if (args.includes('--verify')) {
  verifySetup()
} else {
  setupSaaSDatabase()
}
