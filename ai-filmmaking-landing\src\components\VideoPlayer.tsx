'use client'

import { useEffect, useRef, useState } from 'react'
import { insertVideoAnalytics, updateVideoAnalytics } from '@/lib/supabase'

interface VideoPlayerProps {
  viewerId: string
}

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
  }
}

export default function VideoPlayer({ viewerId }: VideoPlayerProps) {
  const playerRef = useRef<any>(null)
  const [analyticsId, setAnalyticsId] = useState<string | null>(null)
  const [sessionStart] = useState(new Date().toISOString())
  const [lastPosition, setLastPosition] = useState(0)
  const [totalWatchTime, setTotalWatchTime] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const videoId = process.env.NEXT_PUBLIC_YOUTUBE_VIDEO_ID || 's-hLAqtNJvg'
  const videoDuration = 707 // 11:47 in seconds

  useEffect(() => {
    // Load YouTube IFrame API
    if (!window.YT) {
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
    }

    // Initialize player when API is ready
    const initializePlayer = () => {
      if (window.YT && window.YT.Player) {
        playerRef.current = new window.YT.Player('youtube-player', {
          height: '100%',
          width: '100%',
          videoId: videoId,
          playerVars: {
            autoplay: 1,
            controls: 0,
            disablekb: 1,
            fs: 0,
            iv_load_policy: 3,
            modestbranding: 1,
            playsinline: 1,
            rel: 0,
            showinfo: 0,
            cc_load_policy: 0,
            hl: 'en',
            color: 'white'
          },
          events: {
            onReady: onPlayerReady,
            onStateChange: onPlayerStateChange
          }
        })
      }
    }

    if (window.YT && window.YT.Player) {
      initializePlayer()
    } else {
      window.onYouTubeIframeAPIReady = initializePlayer
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      if (playerRef.current && playerRef.current.destroy) {
        playerRef.current.destroy()
      }
    }
  }, [viewerId])

  const onPlayerReady = async () => {
    // Create initial analytics record
    try {
      const analytics = await insertVideoAnalytics({
        viewer_id: viewerId,
        video_id: videoId,
        watch_time: 0,
        total_duration: videoDuration,
        completion_percentage: 0,
        current_position: 0,
        session_start: sessionStart
      })
      setAnalyticsId(analytics.id)
    } catch (error) {
      console.error('Error creating analytics record:', error)
    }
  }

  const onPlayerStateChange = (event: any) => {
    const state = event.data
    
    if (state === window.YT.PlayerState.PLAYING) {
      startTracking()
    } else if (state === window.YT.PlayerState.PAUSED || 
               state === window.YT.PlayerState.ENDED) {
      stopTracking()
      updateAnalytics()
    }
  }

  const startTracking = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    intervalRef.current = setInterval(() => {
      if (playerRef.current && playerRef.current.getCurrentTime) {
        const currentTime = playerRef.current.getCurrentTime()
        const timeDiff = Math.abs(currentTime - lastPosition)
        
        // Only count as watch time if the difference is reasonable (not seeking)
        if (timeDiff < 2) {
          setTotalWatchTime(prev => prev + 1)
        }
        
        setLastPosition(currentTime)
        
        // Update analytics every 10 seconds
        if (totalWatchTime % 10 === 0) {
          updateAnalytics(currentTime)
        }
      }
    }, 1000)
  }

  const stopTracking = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  const updateAnalytics = async (currentPosition?: number) => {
    if (!analyticsId) return

    const position = currentPosition || (playerRef.current?.getCurrentTime() || 0)
    const completionPercentage = (totalWatchTime / videoDuration) * 100

    try {
      await updateVideoAnalytics(analyticsId, {
        watch_time: totalWatchTime,
        completion_percentage: Math.min(completionPercentage, 100),
        current_position: Math.floor(position),
        session_end: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error updating analytics:', error)
    }
  }

  // Update analytics when component unmounts
  useEffect(() => {
    return () => {
      updateAnalytics()
    }
  }, [totalWatchTime, analyticsId])

  return (
    <div className="relative w-full h-full bg-black">
      <div id="youtube-player" className="w-full h-full" />
      
      {/* Custom overlay to prevent right-click and other interactions */}
      <div className="absolute inset-0 pointer-events-none" />
      
      {/* Progress indicator (optional) */}
      <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-1">
        <span className="text-white text-sm">
          Watch time: {Math.floor(totalWatchTime / 60)}:{(totalWatchTime % 60).toString().padStart(2, '0')}
        </span>
      </div>
    </div>
  )
}
