# AI Filmmaking Course Landing Page

A modern, responsive landing page for an AI Filmmaking course with advanced video tracking capabilities.

## Features

- 🎬 **Professional Landing Page**: Modern design with gradient backgrounds and smooth animations
- 📹 **Video Modal with Lead Capture**: Popup form to collect user information before video access
- 🔒 **Custom Video Player**: YouTube embed with hidden controls and suggestions
- 📊 **Advanced Analytics**: Track video viewing progress, completion rates, and user engagement
- 📱 **Fully Responsive**: Optimized for all devices and screen sizes
- 🚀 **Built with Next.js**: Modern React framework with TypeScript support

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase
- **Video**: YouTube IFrame Player API
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Set up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. In the SQL Editor, run the database schema from `database-schema.sql`

### 3. Configure Environment Variables

Update `.env.local` with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_YOUTUBE_VIDEO_ID=s-hLAqtNJvg
```

### 4. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the landing page.

## Video Tracking Features

### User Experience
1. User clicks the video play button
2. Modal popup appears with name/email form
3. After form submission, video starts playing with:
   - Hidden YouTube controls
   - No related video suggestions
   - Custom progress tracking

### Analytics Tracked
- **User Information**: Name and email from lead capture form
- **Watch Time**: Total time spent watching (excluding seeks)
- **Completion Percentage**: How much of the video was watched
- **Current Position**: Last position in the video
- **Session Data**: Start and end times
- **Milestones**: 25%, 50%, 75%, and 100% completion markers

### Admin Dashboard
Access the analytics dashboard at `/admin` to view:
- Total viewers count
- Average watch time
- Average completion rate
- Individual viewer details with progress bars
