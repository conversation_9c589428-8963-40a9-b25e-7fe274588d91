-- Create video_viewers table
CREATE TABLE video_viewers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create video_analytics table
CREATE TABLE video_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  viewer_id UUID REFERENCES video_viewers(id) ON DELETE CASCADE,
  video_id VARCHAR(255) NOT NULL,
  watch_time INTEGER DEFAULT 0, -- in seconds
  total_duration INTEGER NOT NULL, -- in seconds
  completion_percentage DECIMAL(5,2) DEFAULT 0.00,
  current_position INTEGER DEFAULT 0, -- in seconds
  session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX idx_video_analytics_viewer_id ON video_analytics(viewer_id);
CREATE INDEX idx_video_analytics_video_id ON video_analytics(video_id);
CREATE INDEX idx_video_viewers_email ON video_viewers(email);

-- Enable Row Level Security (RLS)
ALTER TABLE video_viewers ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for your security requirements)
CREATE POLICY "Allow public insert on video_viewers" ON video_viewers
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on video_viewers" ON video_viewers
  FOR SELECT USING (true);

CREATE POLICY "Allow public insert on video_analytics" ON video_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public update on video_analytics" ON video_analytics
  FOR UPDATE USING (true);

CREATE POLICY "Allow public select on video_analytics" ON video_analytics
  FOR SELECT USING (true);
